import { z } from "zod";
import { createTR<PERSON>Router, publicProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { callAnalysis, calls } from "~/server/db/schema";
import { eq } from "drizzle-orm";

export const sonexaRouter = createTRPCRouter({

  getCallsByUserId: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(calls)
        .where(eq(calls.userId, input));
      return result;
    }),

  storeCallRecord: publicProcedure
    .input(z.string())
    .mutation(async ({ input }) => {
      const result = await db
        .insert(calls)
        .values({
          id: input,
          userId: "c9931794-b648-469f-b02c-14ed048edaba",
          isSettled: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      return result;
    }),

  // Get call analysis by call_id
  getCallAnalysisByCallId: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(callAnalysis)
        .where(eq(callAnalysis.call_id, input));
      return result[0] ?? null; // Return single record or null
    }),

});
