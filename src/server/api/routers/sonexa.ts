import { z } from "zod";
import * as Sentry from "@sentry/nextjs";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { callAnalysis, calls } from "~/server/db/schema";
import { eq } from "drizzle-orm";
import { TRPCError } from "@trpc/server";

export const sonexaRouter = createTRPCRouter({

  getCallsByUserId: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      try {
        const result = await db
          .select()
          .from(calls)
          .where(eq(calls.userId, input));

        return result;
      } catch (err) {
        console.error(`[getCallsByUserId] Database error for user ${input}:`, err);
        Sentry.captureException(err);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send call',
          cause: err,
        });
      }
    }),

  storeCallRecord: publicProcedure
    .input(z.string().uuid())
    .mutation(async ({ input }) => {
      try {
        const result = await db
          .insert(calls)
          .values({
            id: input,
            userId: "c9931794-b648-469f-b02c-14ed048edaba",
            status: "new",
          })
          .returning();

        return { success: true, call: result[0] };
      } catch (err) {
        Sentry.captureException(err);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send call',
          cause: err,
        });
      }
    }),

  // Get call analysis by call_id
  getCallAnalysisByCallId: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input }) => {
      try {
        const result = await db
          .select()
          .from(callAnalysis)
          .where(eq(callAnalysis.callId, input))
          .limit(1);

        const analysis = result[0] ?? null;

        return analysis;
      } catch (err) {
        Sentry.captureException(err);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send call',
          cause: err,
        });
      }
    }),

    setCallToInProgress: publicProcedure
      .input(z.string().uuid())
      .mutation(async ({ input }) => {
        try {
          const result = await db
            .update(calls)
            .set({ status: "in_progress" })
            .where(eq(calls.id, input))
            .returning();

          return { success: true, data: result[0] ?? null };
        } catch (err) {
          Sentry.captureException(err);
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to send call',
            cause: err,
          });
        }
      }),

    setCallToCompleted: publicProcedure
      .input(z.string().uuid())
      .mutation(async ({ input }) => {
        try {
          const result = await db
            .update(calls)
            .set({ status: "completed" })
            .where(eq(calls.id, input))
            .returning();

          return { success: true, data: result[0] ?? null };
        } catch (err) {
          Sentry.captureException(err);
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to send call',
            cause: err,
          });
        }
      }),

});
