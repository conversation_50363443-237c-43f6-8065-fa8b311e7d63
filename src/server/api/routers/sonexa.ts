import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { callAnalysis, calls } from "~/server/db/schema";
import { eq } from "drizzle-orm";
import { TRPCError } from "@trpc/server";

export const sonexaRouter = createTRPCRouter({

  getCallsByUserId: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      try {
        console.log(`[getCallsByUserId] Fetching calls for user: ${input}`);

        const result = await db
          .select()
          .from(calls)
          .where(eq(calls.userId, input));

        console.log(`[getCallsByUserId] Found ${result.length} calls for user: ${input}`);

        return result;
      } catch (error) {
        console.error(`[getCallsByUserId] Database error for user ${input}:`, error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch calls for user',
          cause: error,
        });
      }
    }),

  storeCallRecord: publicProcedure
    .input(z.string().uuid())
    .mutation(async ({ input }) => {
      try {
        console.log(`[storeCallRecord] Storing call record with ID: ${input}`);

        const result = await db
          .insert(calls)
          .values({
            id: input,
            userId: "c9931794-b648-469f-b02c-14ed048edaba",
            isSettled: false,
          })
          .returning();

        return { success: true, call: result[0] };
      } catch (error) {
        console.error(`[storeCallRecord] Error storing call ${input}:`, error);

        if (error instanceof TRPCError) {
          throw error; // Re-throw TRPCErrors as-is
        }

        // Handle database constraint errors
        if (error instanceof Error && error.message.includes('duplicate key')) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Call record already exists',
            cause: error,
          });
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to store call record',
          cause: error,
        });
      }
    }),

  // Get call analysis by call_id
  getCallAnalysisByCallId: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input }) => {
      try {
        console.log(`[getCallAnalysisByCallId] Fetching analysis for call: ${input}`);

        const result = await db
          .select()
          .from(callAnalysis)
          .where(eq(callAnalysis.callId, input))
          .limit(1);

        const analysis = result[0] ?? null;

        if (analysis) {
          console.log(`[getCallAnalysisByCallId] Found analysis for call: ${input}`);
        } else {
          console.log(`[getCallAnalysisByCallId] No analysis found for call: ${input}`);
        }

        return analysis;
      } catch (error) {
        console.error(`[getCallAnalysisByCallId] Database error for call ${input}:`, error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch call analysis',
          cause: error,
        });
      }
    }),

});
