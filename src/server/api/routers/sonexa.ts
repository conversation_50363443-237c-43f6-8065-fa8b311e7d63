import { z } from "zod";
import { createTR<PERSON><PERSON><PERSON>er, publicProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";
import { callAnalysis } from "~/server/db/schema";
import { eq } from "drizzle-orm";

export const sonexaRouter = createTRPCRouter({
  // Get call analysis by call_id
  getCallAnalysis: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(callAnalysis)
        .where(eq(callAnalysis.call_id, input));
      return result[0] ?? null; // Return single record or null
    }),

  // Get all call analyses
  getAllCallAnalyses: publicProcedure
    .query(async () => {
      const result = await db
        .select()
        .from(callAnalysis)
        .orderBy(callAnalysis.created_at);
      return result;
    }),

  // Get call analyses by user_id
  getCallAnalysesByUser: publicProcedure
    .input(z.string())
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(callAnalysis)
        .where(eq(callAnalysis.user_id, input))
        .orderBy(callAnalysis.created_at);
      return result;
    }),

  // Get call analyses by sentiment
  getCallAnalysesBySentiment: publicProcedure
    .input(z.enum(["positive", "neutral", "negative"]))
    .query(async ({ input }) => {
      const result = await db
        .select()
        .from(callAnalysis)
        .where(eq(callAnalysis.call_sentiment, input))
        .orderBy(callAnalysis.created_at);
      return result;
    }),
});
