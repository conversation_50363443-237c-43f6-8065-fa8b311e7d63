
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { getBlandApiKey } from "~/lib/call-helpers";
import { blandAccountStatusSchema, 
  CallResponseSchema, 
  BlandCallRequestSchema, 
  blandCallHistorySchema, 
  blandCallDetailSchema,
  blandWebhookRequestSchema 
} from "~/lib/schemas";


export const blandRouter = createTRPCRouter({
  getMe: publicProcedure.query(async () => {
    const options = {
      method: 'GET',
      headers: {
        authorization: getBlandApiKey(),
      }
    };

    try {
      const response = await fetch('https://api.bland.ai/v1/me', options);
      return blandAccountStatusSchema.parse(await response.json());

    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching account details:', error.message);
      } else {
        console.error('Unknown error fetching account details:', error);
      }
      throw new Error('Failed to fetch account details');
    }
  }),

  sendCall: publicProcedure
    .input(BlandCallRequestSchema)
    .mutation(async ({ input }) => {
      try {
        const response = await fetch('https://api.bland.ai/v1/calls', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: getBlandApiKey(),
          },
          body: JSON.stringify(input),
        });

        if (!response.ok) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to send call',
            cause: response.statusText,
          });
        }

        const responseData = await response.json();
        return CallResponseSchema.parse(responseData);
        
      } catch (err) {
        console.error('Error sending call:', err);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send call',
          cause: err,
        });
      }
    }),


  getListCalls: publicProcedure
    .mutation(async () => {
      const options = {
        method: 'GET',
        headers: {
          authorization: getBlandApiKey(),
        }
      };

    try {
      const response = await fetch('https://api.bland.ai/v1/calls', options);
      return blandCallHistorySchema.parse(await response.json());

    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching call history:', error.message);
        throw new Error('Failed to fetch call list');
      } else {
        console.error('Unknown error fetching call history:', error);
        throw new Error('Failed to fetch call list');
      }
    }
  }),

  getCallDetails: publicProcedure
    .input(z.string())
    .mutation(async ({ input }) => {
      const options = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          authorization: getBlandApiKey(),
        },
      };

      try {
        const response = await fetch(`https://api.bland.ai/v1/calls/${input}`, options);
        return blandCallDetailSchema.parse(await response.json());
      } catch (error: unknown) {
        if (error instanceof Error) {
          console.error('Error getting call details', error.message);
        } else {
          console.error('Unknown error getting call details:', error);
        }
        throw new Error('Failed to get call details');
      }
    }),

  //not currently used
  postCallWebhook: publicProcedure
    .input(blandWebhookRequestSchema)
    .mutation(async ({ input }) => {
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          authorization: getBlandApiKey(),
        },
        body: JSON.stringify(input),
      };

      try {
        const response = await fetch('https://api.bland.ai/v1/postcall/webhooks/create', options);
        return response.json();
        // return blandCallDetailSchema.parse(await response.json());
      } catch (error: unknown) {
        if (error instanceof Error) {
          console.error('Error setting up webhook', error.message);
        } else {
          console.error('Unknown error setting up webhook:', error);
        }
        throw new Error('Failed to set up webhook');
      }
    }),
});
