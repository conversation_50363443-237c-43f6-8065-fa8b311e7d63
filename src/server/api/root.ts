
import { createCallerFactory, createTR<PERSON><PERSON>outer } from "~/server/api/trpc";
import { blandRouter } from "./routers/bland";
import { sonexaRouter } from "./routers/sonexa";
// import { type AnyRouter } from "@trpc/server";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    bland: blandRouter,
    sonexa: sonexaRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
