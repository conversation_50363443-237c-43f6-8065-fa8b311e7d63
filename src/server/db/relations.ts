import { relations } from "drizzle-orm/relations";
import { businesses, user, callAnalysis } from "../../../drizzle/schema";

export const userRelations = relations(user, ({one}) => ({
	business: one(businesses, {
		fields: [user.businessId],
		references: [businesses.id]
	}),
}));

export const businessesRelations = relations(businesses, ({many}) => ({
	users: many(user),
	callAnalyses: many(callAnalysis),
}));

export const callAnalysisRelations = relations(callAnalysis, ({one}) => ({
	business: one(businesses, {
		fields: [callAnalysis.businessId],
		references: [businesses.id]
	}),
}));