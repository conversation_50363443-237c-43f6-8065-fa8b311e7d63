import { drizzle } from "drizzle-orm/postgres-js";
import * as Sentry from "@sentry/nextjs";
import postgres from "postgres";
import { env } from "~/env";

/**
 * Cache the database connection in development.
 */
const globalForDb = globalThis as unknown as {
  conn: ReturnType<typeof postgres> | undefined;
};

const connectionString = env.DATABASE_URL;

if (!connectionString) {
  Sentry.captureException("no connection string provided");
  throw new Error("no connection string provided");
}

// Configure SSL based on environment
const sslConfig = env.NODE_ENV === "production" ? "require" : false;

const client = globalForDb.conn ?? postgres(connectionString, {
  max: 1,
  ssl: sslConfig
});

if (env.NODE_ENV !== "production") globalForDb.conn = client;

export const db = drizzle(client); // ⛔ DO NOT pass `{ schema }`
