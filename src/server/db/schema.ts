import { pgTable, foreignKey, text, integer, timestamp, unique, pgPolicy, uuid, boolean, check } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const users = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text(),
	email: text(),
	emailVerified: timestamp({ mode: 'string' }),
	businessId: uuid("business_id"),
}, (table) => [
	foreignKey({
		columns: [table.businessId],
		foreignColumns: [businesses.id],
		name: "user_business_id_fkey"
	}).onDelete("cascade"),
	unique("user_email_unique").on(table.email),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"], using: sql`true` }),
]);

export const businesses = pgTable("businesses", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	email: text(),
	phone: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"], using: sql`true` }),
]);

export const callAnalysis = pgTable("call_analysis", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	user_id: text().notNull(),
	call_id: text().notNull(),
	call_summary: text(),
	call_sentiment: text({ enum: ["positive", "neutral", "negative"] }).notNull(),
    complaint: boolean().notNull(),
    compliment: boolean().notNull(),
    products_mentioned: text().array(),
    actionable_suggestions: text().array(),
    urgency: text({ enum: ["low", "medium", "high"] }).notNull(),
    topics: text().array(),
    customer_satisfaction_score: integer().notNull(),
	created_at: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updated_at: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	check("customer_satisfaction_check", sql`customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10`),
	foreignKey({
		columns: [table.user_id],
		foreignColumns: [users.id],
		name: "call_analysis_user_id_fkey"
	}),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"] }),
]);