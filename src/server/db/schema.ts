import {
	pgTable, foreignKey, text, integer, timestamp, boolean, check, uuid, unique, pgPolicy, bigint
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

// --- Users Table ---
export const users = pgTable("users", {
	id: text().primaryKey().notNull(),
	name: text(),
	email: text(),
	createdAt: timestamp("created_at", { mode: "string" }).defaultNow(),
}, (table) => [
	unique("user_email_unique").on(table.email),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"], using: sql`true` }),
]);

// --- Calls Table ---
export const calls = pgTable("calls", {
	id: bigint("id", { mode: "number" }).primaryKey().notNull(),
	user_id: text().notNull(),
	completed: boolean().notNull().default(false),
	created_at: timestamp("created_at", { mode: "string" }).defaultNow(),
	updated_at: timestamp("updated_at", { mode: "string" }).defaultNow(),
}, (table) => [
	foreignKey({
		columns: [table.user_id],
		foreignColumns: [users.id],
		name: "calls_user_id_fkey"
	}).onDelete("cascade"),
]);

// --- Call Analysis Table ---
export const callAnalysis = pgTable("call_analysis", {
	id: bigint("id", { mode: "number" }).primaryKey().notNull(),
	call_id: text("call_id").notNull(),
	user_id: text().notNull(),
	call_summary: text(),
	call_sentiment: text({ enum: ["positive", "neutral", "negative"] }).notNull(),
	complaint: boolean().notNull(),
	compliment: boolean().notNull(),
	products_mentioned: text().array(),
	actionable_suggestions: text().array(),
	urgency: text({ enum: ["low", "medium", "high"] }).notNull(),
	topics: text().array(),
	customer_satisfaction_score: integer().notNull(),
	created_at: timestamp("created_at", { mode: "string" }).defaultNow(),
	updated_at: timestamp("updated_at", { mode: "string" }).defaultNow(),
}, (table) => [
	check("customer_satisfaction_check", sql`customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10`),
	foreignKey({
		columns: [table.user_id],
		foreignColumns: [users.id],
		name: "call_analysis_user_id_fkey"
	}).onDelete("cascade"),
	foreignKey({
		columns: [table.call_id],
		foreignColumns: [calls.id],
		name: "call_analysis_call_id_fkey"
	}).onDelete("cascade"),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"] }),
]);
