import { db } from "~/server/db";
import { calls } from "~/server/db/schema";
import { eq } from "drizzle-orm";

export function getBlandApiKey(): string {
  const apiKey = process.env.BLAND_API_KEY;
  if (!apiKey) {
      throw new Error("BLAND_API_KEY is not defined");
  }
  return apiKey;
}

export async function buildAndReturnAiCallAnalysisPrompt(transcript: string) {
    const aiAnalysisPrompt = `You are an AI assistant analyzing a customer service call transcript. 
    Your goal is to assess only the user's side of the conversation (ignore the assistant's or agent's lines) 
    and respond exclusively with a JSON object matching this schema:

    {
        "sentiment": "positive" | "neutral" | "negative",
        "complaint": true | false,
        "compliment": true | false,
        "products_mentioned": ["string", ...],
        "actionable_suggestions": ["string", ...],
        "urgency": "low" | "medium" | "high",
        "topics": ["string", ...],
        "customer_satisfaction_score": integer (1-10)
    }

    Important:
    - Analyze only the user's transcript for sentiment, topics, complaints, compliments, urgency, actionable suggestions, and customer satisfaction.
    - Do not analyze or consider any text from the assistant/agent.
    - Do not provide any commentary or explanation.
    - Respond only with a valid JSON object.

    Transcript:
    ${transcript}
    `;

    return aiAnalysisPrompt;

}

export async function setCallToSettled (callId: string) {
  try {
    const result = await db
      .update(calls)
      .set({ isSettled: true })
      .where(eq(calls.id, callId))
      .returning();

    if (result.length === 0) {
        throw new Error(`No call found with ID ${callId}`);
    } 

  } catch (error) {
    throw new Error(`[setCallToSettled] Error setting call ${callId} to settled: ${error}`);
  }
}
