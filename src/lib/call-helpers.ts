
import * as Sentry from "@sentry/nextjs";
import { db } from "~/server/db";
import crypto from 'crypto';
import { calls, callAnalysis } from "~/server/db/schema";
import { eq } from "drizzle-orm";

export async function buildAndReturnAiCallAnalysisPrompt(transcript: string) {
    const aiAnalysisPrompt = `You are an AI assistant analyzing a customer service call transcript. 
    Your goal is to assess only the user's side of the conversation (ignore the assistant's or agent's lines) 
    and respond exclusively with a JSON object matching this schema:

    {
        "sentiment": "positive" | "neutral" | "negative",
        "complaint": true | false,
        "compliment": true | false,
        "products_mentioned": ["string", ...],
        "actionable_suggestions": ["string", ...],
        "urgency": "low" | "medium" | "high",
        "topics": ["string", ...],
        "customer_satisfaction_score": integer (1-10)
    }

    Important:
    - Analyze only the user's transcript for sentiment, topics, complaints, compliments, urgency, actionable suggestions, and customer satisfaction.
    - Do not analyze or consider any text from the assistant/agent.
    - Do not provide any commentary or explanation.
    - Respond only with a valid JSON object.

    Transcript:
    ${transcript}
    `;

    return aiAnalysisPrompt;

}

export async function storeCallAnalysisInDb (callId: string, analysis: any) {
  try {
    const result = await db
      .insert(callAnalysis)
      .values({
        callId: callId,
        userId: "c9931794-b648-469f-b02c-14ed048edaba",
        callSummary: analysis.call_summary,
        callSentiment: analysis.call_sentiment,
        complaint: analysis.complaint,
        compliment: analysis.compliment,
        productsMentioned: analysis.products_mentioned,
        actionableSuggestions: analysis.actionable_suggestions,
        urgency: analysis.urgency,
        topics: analysis.topics,
        customerSatisfactionScore: analysis.customer_satisfaction_score,
      })
      .returning();

      return result;

  } catch (err) {
    Sentry.captureException(err);
    throw new Error(`[storeCallAnalysisInDb] Error storing call analysis for call ${callId}: ${err}`);
  }
}

export async function setCallToCompleted (callId: string) {
  try {
    const result = await db
      .update(calls)
      .set({ status: "completed" })
      .where(eq(calls.id, callId))
      .returning();

    if (result.length === 0) {
        Sentry.captureException(`No call found with ID ${callId}`);
        throw new Error(`No call found with ID ${callId}`);
    } 

  } catch (err) {
    Sentry.captureException(err);
    throw new Error(`[setCallToSettled] Error setting call ${callId} to settled: ${err}`);
  }
}

export function verifyWebhookSignature(key: string, data: string, signature: string) {
  const expectedSignature = crypto
      .createHmac('sha256', key)
      .update(data)
      .digest('hex');
  return expectedSignature === signature;
}
