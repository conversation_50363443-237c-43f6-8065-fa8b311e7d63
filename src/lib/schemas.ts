import {z} from "zod";

export const BlandCallRequestSchema = z.object({
  phone_number: z.string(),
  task: z.string(),
  wait_for_greeting: z.boolean(),
  temperature: z.number(),
  local_dialing: z.boolean(),
  noise_cancellation: z.boolean(),
  ignore_button_press: z.boolean(),
  max_duration: z.number(),
  metadata: z.object({
    businessId: z.string(),
  }),
  webhook: z.string(),
});

export const CallResponseSchema = z.object({
  status: z.literal("success"),
  message: z.string(),
  call_id: z.string().uuid(),
  batch_id: z.string().uuid().nullable(), // nullable UUID
});

export const blandAccountStatusSchema = z.object({
  status: z.literal("active").or(z.literal("inactive")),
  billing: z.object({
    current_balance: z.number(),
    refill_to: z.number().nullable(),
  }),
  total_calls: z.number(),
});

export const blandCallHistorySchema = z.object({
  status: z.string(),
  total_count: z.number(),
  count: z.number(),
  calls: z.array(
    z.object({
      c_id: z.string().uuid(),
      created_at: z.string().datetime(),
      call_length: z.number(),
      to: z.string(),
      completed: z.boolean(),
      started_at: z.string().datetime(),
      summary: z.string().nullable(),
      call_ended_by: z.string().nullable(),
    }).passthrough()
  ),
});

export const blandCallDetailSchema = z.object({
  call_id: z.string(),
  call_length: z.number(),
  to: z.string(),
  created_at: z.string(),
  summary: z.string().nullable(),
  price: z.number(),
  concatenated_transcript: z.string().nullable(),
  completed: z.boolean(),
  transcripts: z.array(
    z.object({
      id: z.number(),
      created_at: z.string().datetime(),
      text: z.string(),
      user: z.string(),
    }).passthrough()
  ),
}).passthrough();

export const blandWebhookRequestSchema = z.object({
  webhook_url: z.string(),
  call_ids: z.array(z.string()),
});

