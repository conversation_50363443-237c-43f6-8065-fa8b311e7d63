// ~/lib/supabaseClient.ts
import { createClient } from "@supabase/supabase-js";
import { env } from "~/env";

if (process.env.NODE_ENV === "production") {
    if (!env.NEXT_PUBLIC_SUPABASE_URL || !env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
        throw new Error("Supabase environment variables are not defined");
    }
}

if (process.env.NODE_ENV !== "production") {
    if (!env.NEXT_PUBLIC_SUPABASE_LOCAL_URL || !env.NEXT_PUBLIC_SUPABASE_LOCAL_ANON_KEY) {
        throw new Error("Supabase environment variables are not defined");
    }
}

// Create a single client for the entire app
export const supabase = createClient(
  process.env.NODE_ENV === "production" ? env.NEXT_PUBLIC_SUPABASE_URL : env.NEXT_PUBLIC_SUPABASE_LOCAL_URL,
  process.env.NODE_ENV === "production" ? env.NEXT_PUBLIC_SUPABASE_ANON_KEY : env.NEXT_PUBLIC_SUPABASE_LOCAL_ANON_KEY,
);
