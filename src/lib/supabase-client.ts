// ~/lib/supabaseClient.ts
import { createClient } from "@supabase/supabase-js";
import { env } from "~/env";

let supabaseUrl: string;
let supabaseKey: string;

if (process.env.NODE_ENV === "production") {
  supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL!;
  supabaseKey = env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
} else {
  supabaseUrl = env.NEXT_PUBLIC_SUPABASE_LOCAL_URL!;
  supabaseKey = env.NEXT_PUBLIC_SUPABASE_LOCAL_ANON_KEY!;
}

export const supabase = createClient(supabaseUrl, supabaseKey);
