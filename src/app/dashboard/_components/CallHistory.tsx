import Link from "next/link";
import { useState } from "react";
import { api } from "~/trpc/react";
import { useRealtimeCallStatus } from "~/app/hooks/useRealtimeCallStatus";
import { Phone, Clock, User, ExternalLink, Activity } from "lucide-react";

type CallListItem = {
  id: string;
  userId: string;
  status: string;
  createdAt: string | null;
  updatedAt: string | null;
}

export default function CallHistory({ userId }: { userId: string }) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const { data: callHistory = [], isLoading, refetch: refetchCallHistory } = api.sonexa.getCallsByUserId.useQuery(userId,{enabled: !!userId});
  useRealtimeCallStatus(() => {
    refetchCallHistory();
  });

  // Pagination calculations
  const totalPages = Math.ceil(callHistory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCalls = callHistory.slice(startIndex, endIndex);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-500/10 rounded-lg">
            <Phone className="h-5 w-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Call History</h3>
            <p className="text-sm text-gray-400">Track your recent calls and their status</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <Activity className="h-4 w-4" />
          <span>{callHistory.length} total calls</span>
        </div>
      </div>

      {isLoading ? (
        <LoadingState />
      ) : callHistory.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="space-y-4">
          <PaginationInfo
            startIndex={startIndex}
            endIndex={endIndex}
            totalItems={callHistory.length}
          />
          <CallList calls={currentCalls} />
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="flex items-center space-x-3">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
        <p className="text-blue-400 font-medium">Loading call history...</p>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="text-center py-12 bg-slate-800/50 rounded-xl border border-slate-700">
      <div className="p-3 bg-gray-500/10 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
        <Phone className="h-8 w-8 text-gray-400" />
      </div>
      <h4 className="text-lg font-medium text-white mb-2">No calls yet</h4>
      <p className="text-gray-400 mb-4">Start making calls to see your history here</p>
    </div>
  );
}

function PaginationInfo({ startIndex, endIndex, totalItems }: {
  startIndex: number;
  endIndex: number;
  totalItems: number
}) {
  return (
    <div className="flex items-center justify-between text-sm text-gray-400 bg-slate-800/30 px-4 py-2 rounded-lg">
      <span>
        Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} calls
      </span>
      <div className="flex items-center space-x-1">
        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
        <span>Live updates enabled</span>
      </div>
    </div>
  );
}

function CallList({ calls }: { calls: CallListItem[] }) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-500/10 text-green-400 border-green-500/20';
      case 'in_progress': return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
      case 'new': return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20';
      case 'failed': return 'bg-red-500/10 text-red-400 border-red-500/20';
      case 'cancelled': return 'bg-gray-500/10 text-gray-400 border-gray-500/20';
      default: return 'bg-gray-500/10 text-gray-400 border-gray-500/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return '✓';
      case 'in_progress': return '⟳';
      case 'new': return '○';
      case 'failed': return '✗';
      case 'cancelled': return '⊘';
      default: return '○';
    }
  };

  return (
    <div className="grid gap-3">
      {calls.map((call, i) => (
        <Link
          href={`/calls/${call.id}`}
          key={call.id ?? i}
          className="group bg-slate-800/50 hover:bg-slate-800 border border-slate-700 hover:border-slate-600 rounded-xl p-4 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/5"
        >
          <div className="flex items-center justify-between">
            {/* Left side - Call info */}
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors">
                <Phone className="h-4 w-4 text-blue-400" />
              </div>
              <div className="flex items-center">
                <div className="flex items-center space-x-2 mr-6">
                  <h4 className="font-medium text-sm text-white group-hover:text-blue-300 transition-colors">
                    Call #{call.id.slice(-8)}
                  </h4>
                  <ExternalLink className="h-3 w-3 text-gray-500 group-hover:text-blue-400 transition-colors" />
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1 text-sm text-gray-400">
                    <Clock className="h-3 w-3" />
                    <span>
                      {call.createdAt ? new Date(call.createdAt).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      }) : 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Status */}
            <div className={`px-3 py-1 rounded-full border text-xs font-medium flex items-center space-x-1 ${getStatusColor(call.status)}`}>
              <span>{getStatusIcon(call.status)}</span>
              <span className="capitalize">{call.status.replace('_', ' ')}</span>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}

function PaginationControls({
  currentPage,
  totalPages,
  onPageChange
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void
}) {
  if (totalPages <= 1) return null;

  const handlePreviousPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  // Smart pagination - show max 5 page numbers with ellipsis
  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = totalPages > 1 ? getVisiblePages() : [1];

  return (
    <div className="flex items-center justify-center">
      <div className="flex items-center bg-slate-800/50 rounded-xl border border-slate-700 p-1">
        {/* Previous Button */}
        <button
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
          className={`
            flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
            ${currentPage === 1
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
            }
          `}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span className="hidden sm:inline">Previous</span>
        </button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1 mx-2">
          {visiblePages.map((page, index) => (
            page === '...' ? (
              <span key={`ellipsis-${index}`} className="px-2 py-1 text-gray-500">
                ⋯
              </span>
            ) : (
              <button
                key={page}
                onClick={() => onPageChange(page as number)}
                className={`
                  w-8 h-8 rounded-lg text-sm font-medium transition-all duration-200
                  ${currentPage === page
                    ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/25'
                    : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
                  }
                `}
              >
                {page}
              </button>
            )
          ))}
        </div>

        {/* Next Button */}
        <button
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          className={`
            flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
            ${currentPage === totalPages
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
            }
          `}
        >
          <span className="hidden sm:inline">Next</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
}