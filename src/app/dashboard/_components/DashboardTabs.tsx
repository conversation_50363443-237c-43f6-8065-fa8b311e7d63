import { api } from "~/trpc/react";

interface DashboardTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function DashboardTabs({ activeTab, setActiveTab }: DashboardTabsProps) {
  // const getListCalls = api.bland.getListCalls.useMutation();

  const handleSelectCallHistory = async (tab: string) => {
    setActiveTab(tab);
    // if (tab === "history") {
    //   try {
    //     await getListCalls.mutateAsync();
    //   } catch (error) {
    //     console.error("Error fetching call history:", error);
    //   }
    // }
  };

  return (
    <div className="flex border-b border-slate-700">
      <TabButton 
        isActive={activeTab === "create"}
        onClick={() => setActiveTab("create")}
        label="Create Agent"
      />
      <TabButton 
        isActive={activeTab === "history"}
        onClick={() => setActiveTab("history")}
        label="Call History"
      />
      <TabButton 
        isActive={activeTab === "analytics"}
        onClick={() => setActiveTab("analytics")}
        label="Analytics"
      />
    </div>
  );
}

interface TabButtonProps {
  isActive: boolean;
  onClick: () => void;
  label: string;
}

function TabButton({ isActive, onClick, label }: TabButtonProps) {
  return (
    <button
      className={`px-6 py-4 text-sm font-medium ${
        isActive
          ? "text-blue-400 border-b-2 border-blue-400"
          : "text-gray-400 hover:text-gray-300"
      }`}
      onClick={onClick}
    >
      {label}
    </button>
  );
}