
import { Button } from "~/app/components/ui/button";
import { Input } from "~/app/components/ui/input";
import { Textarea } from "~/app/components/ui/textarea";

import { useSendCall } from "~/app/hooks/useSendCall";

export default function CreateAgentForm() {
  const {
    handleSendCall,
    isSendingCall,
    isStoringCall,
    callStatus,
    setAgentName,
    agentName,
    phoneNumber,
    setPhoneNumber,
    setTask,
    task
  } = useSendCall();

  interface FormFieldProps {
    id: string;
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
    helpText?: string;
  }

  function FormField({ 
    id, 
    label, 
    value, 
    onChange, 
    placeholder, 
    helpText 
  }: FormFieldProps) {
    return (
      <div>
        <label htmlFor={id} className="block text-sm font-medium text-gray-300 mb-2">
          {label}
        </label>
        <Input
          type="text"
          id={id}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {helpText && <p className="mt-1 text-xs text-gray-400">{helpText}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white mb-4">Send a Call</h2>
      
      {callStatus === "success" && (
        <div className="bg-green-900/30 border border-green-800 text-green-300 px-4 py-3 rounded-lg mb-6">
          <p>Call sent successfully. View your call history tab to see the call status.</p>
        </div>
      )}

      {callStatus === "error" && (
        <div className="bg-red-800/30 border border-red-800 text-red-100 px-4 py-3 rounded-lg mb-6">
          <p>Error sending call. Please try again or contact your account administrator.</p>
        </div>
      )}
      
      <FormField
        id="agentName"
        label="Agent Name"
        value={agentName}
        onChange={(e) => setAgentName(e.target.value)}
        placeholder="e.g., Customer Satisfaction Survey"
      />

      <div>
        <Textarea 
          id="task" 
          value={task} 
          onChange={(e) => setTask(e.target.value)} 
          placeholder="Enter your task here..." 
          className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
        />
      </div>
      
      <FormField
        id="phoneNumber"
        label="Phone Number"
        value={phoneNumber}
        onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, "").slice(0, 11))}
        placeholder="Enter 11-digit number"
        helpText="Format: 11-digit number without spaces or dashes"
      />

      <div className="pt-4 w-fit text-white">
        <Button
          onClick={handleSendCall}
          disabled={isSendingCall || isStoringCall}
          className="cursor-pointer hover:bg-black"
        >
          {(isSendingCall || isStoringCall) ? "Calling..." : "Call Now"}
        </Button>
      </div>
    </div>
  );
}
