'use client';

import { useState } from "react";

import DashboardHeader from "./DashboardHeader";
import DashboardTabs from "./DashboardTabs";
import CreateAgentForm from "./CreateAgentForm";
import CallHistory from "./CallHistory";
import AnalyticsPanel from "./AnalyticsPanel";
import QuickTips from "./QuickTips";

export default function Dashboard({userId} : { userId: string }) {
    const [activeTab, setActiveTab] = useState("create");
    return (
        <main className="min-h-screen bg-slate-900">
            <div className="container mx-auto px-4 py-8">
                <DashboardHeader />
                
                <div className="bg-slate-800 rounded-xl shadow-xl overflow-hidden mb-8">
                <DashboardTabs 
                    activeTab={activeTab} 
                    setActiveTab={setActiveTab} 
                />
                
                <div className="p-5">
                    {activeTab === "create" && (
                        <CreateAgentForm />
                    )}

                    {activeTab === "history" && (
                        <CallHistory userId={userId} />
                    )}

                    {activeTab === "analytics" && (
                        <AnalyticsPanel />
                    )}
                </div>
                </div>
                
                <QuickTips />
            </div>
        </main>
    );
}