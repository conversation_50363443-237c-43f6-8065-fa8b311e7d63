"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { Button } from "~/app/_components/ui/button";
import { Input } from "~/app/_components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/app/_components/ui/card";
import { Badge } from "~/app/_components/ui/badge";

export default function CallAnalysisDisplay() {
  const [callId, setCallId] = useState("");
  const [selectedSentiment, setSelectedSentiment] = useState<"positive" | "neutral" | "negative">("positive");

  // Query for all call analyses
  const { data: allAnalyses, isLoading: allLoading } = api.sonexa.getAllCallAnalyses.useQuery();

  // Query for specific call analysis by ID
  const { data: specificAnalysis, isLoading: specificLoading, refetch: refetchSpecific } = 
    api.sonexa.getCallAnalysis.useQuery(callId, {
      enabled: false, // Don't auto-fetch, only when button is clicked
    });

  // Query for analyses by sentiment
  const { data: sentimentAnalyses, isLoading: sentimentLoading } = 
    api.sonexa.getCallAnalysesBySentiment.useQuery(selectedSentiment);

  const handleSearchById = () => {
    if (callId.trim()) {
      refetchSpecific();
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "positive": return "bg-green-100 text-green-800";
      case "negative": return "bg-red-100 text-red-800";
      case "neutral": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Call Analysis Dashboard</h1>

      {/* Search by Call ID */}
      <Card>
        <CardHeader>
          <CardTitle>Search by Call ID</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Enter call ID (e.g., call_001)"
              value={callId}
              onChange={(e) => setCallId(e.target.value)}
            />
            <Button onClick={handleSearchById} disabled={specificLoading}>
              {specificLoading ? "Searching..." : "Search"}
            </Button>
          </div>
          
          {specificAnalysis && (
            <div className="mt-4 p-4 border rounded-lg">
              <h3 className="font-semibold">Call Analysis Result:</h3>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <strong>Call ID:</strong> {specificAnalysis.call_id}
                </div>
                <div>
                  <strong>User ID:</strong> {specificAnalysis.user_id}
                </div>
                <div>
                  <strong>Sentiment:</strong> 
                  <Badge className={`ml-2 ${getSentimentColor(specificAnalysis.call_sentiment)}`}>
                    {specificAnalysis.call_sentiment}
                  </Badge>
                </div>
                <div>
                  <strong>Satisfaction Score:</strong> {specificAnalysis.customer_satisfaction_score}/10
                </div>
                <div>
                  <strong>Urgency:</strong>
                  <Badge className={`ml-2 ${getUrgencyColor(specificAnalysis.urgency)}`}>
                    {specificAnalysis.urgency}
                  </Badge>
                </div>
                <div>
                  <strong>Complaint:</strong> {specificAnalysis.complaint ? "Yes" : "No"}
                </div>
              </div>
              
              {specificAnalysis.products_mentioned && specificAnalysis.products_mentioned.length > 0 && (
                <div className="mt-4">
                  <strong>Products Mentioned:</strong>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {specificAnalysis.products_mentioned.map((product, index) => (
                      <Badge key={index} variant="outline">{product}</Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {specificAnalysis.actionable_suggestions && specificAnalysis.actionable_suggestions.length > 0 && (
                <div className="mt-4">
                  <strong>Actionable Suggestions:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {specificAnalysis.actionable_suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm">{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filter by Sentiment */}
      <Card>
        <CardHeader>
          <CardTitle>Filter by Sentiment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            {(["positive", "neutral", "negative"] as const).map((sentiment) => (
              <Button
                key={sentiment}
                variant={selectedSentiment === sentiment ? "default" : "outline"}
                onClick={() => setSelectedSentiment(sentiment)}
              >
                {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
              </Button>
            ))}
          </div>
          
          {sentimentLoading ? (
            <p>Loading...</p>
          ) : (
            <div className="space-y-2">
              <p className="font-semibold">
                {sentimentAnalyses?.length || 0} {selectedSentiment} call(s) found
              </p>
              {sentimentAnalyses?.map((analysis) => (
                <div key={analysis.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{analysis.call_id}</span>
                    <div className="flex gap-2">
                      <Badge className={getSentimentColor(analysis.call_sentiment)}>
                        {analysis.call_sentiment}
                      </Badge>
                      <Badge className={getUrgencyColor(analysis.urgency)}>
                        {analysis.urgency}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        Score: {analysis.customer_satisfaction_score}/10
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* All Call Analyses */}
      <Card>
        <CardHeader>
          <CardTitle>All Call Analyses ({allAnalyses?.length || 0})</CardTitle>
        </CardHeader>
        <CardContent>
          {allLoading ? (
            <p>Loading all analyses...</p>
          ) : (
            <div className="space-y-2">
              {allAnalyses?.map((analysis) => (
                <div key={analysis.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">{analysis.call_id}</span>
                      <span className="text-sm text-gray-600 ml-2">
                        User: {analysis.user_id}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getSentimentColor(analysis.call_sentiment)}>
                        {analysis.call_sentiment}
                      </Badge>
                      <Badge className={getUrgencyColor(analysis.urgency)}>
                        {analysis.urgency}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {analysis.customer_satisfaction_score}/10
                      </span>
                    </div>
                  </div>
                  {analysis.complaint && (
                    <Badge variant="destructive" className="mt-2">Complaint</Badge>
                  )}
                  {analysis.compliment && (
                    <Badge variant="default" className="mt-2 ml-2">Compliment</Badge>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
