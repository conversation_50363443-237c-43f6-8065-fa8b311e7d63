import { useState } from "react";
import { api } from "~/trpc/react";
import { Button } from "~/app/_components/ui/button";
import { Input } from "~/app/_components/ui/input";
import { Textarea } from "~/app/_components/ui/textarea";

export default function CreateAgentForm() {
  const [agentName, setAgentName] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [task, setTask] = useState("");
  
  const {mutateAsync: sendCall, isPending: isSendingCall, error: sendCallError} = api.bland.sendCall.useMutation();

  const handleSendCall = async () => {
    // Validate inputs
    if (!agentName.trim()) {
      alert("Please enter an agent name");
      return;
    }
    
    if (!task.trim()) {
      alert("Please fill in a task description");
      return;
    }

    const phoneRegex = /^\d{11}$/;
    if (!phoneRegex.exec(phoneNumber)) {
      alert("Please enter a valid 11-digit phone number");
      return;
    }

    // Validate environment variable
    const ngrokUrl = process.env.NEXT_PUBLIC_NGROK_URL;
    if (!ngrokUrl) {
      alert("NGROK URL is not configured. Please check your environment variables.");
      return;
    }

    const callData = {
      phone_number: `+44${phoneNumber}`,
      task: task,
      wait_for_greeting: true,
      temperature: 0.5,
      local_dialing: true,
      noise_cancellation: true,
      ignore_button_press: true,
      max_duration: 10,
      voice: "bb88042c-7858-4875-a686-8e193414ded5",
      webhook: `${ngrokUrl}/api/webhooks/sendcall`,
    };

    try {
      const { call_id } = await sendCall(callData);

      await storeCallRecord(call_id);

      setSuccessMessage("Call sent successfully!");
    } catch (error) {
      console.error("Client - Error sending call:", error);
      setSuccessMessage("Client - Failed to send call. Please try again.");
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white mb-4">Send a Call</h2>
      
      {successMessage && (
        <div className="bg-green-900/30 border border-green-800 text-green-300 px-4 py-3 rounded-lg mb-6">
          {successMessage}
        </div>
      )}
      
      <FormField
        id="agentName"
        label="Agent Name"
        value={agentName}
        onChange={(e) => setAgentName(e.target.value)}
        placeholder="e.g., Customer Satisfaction Survey"
      />

      <div>
        <Textarea 
          id="task" 
          value={task} 
          onChange={(e) => setTask(e.target.value)} 
          placeholder="Enter your task here..." 
          className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
        />
      </div>
      
      <FormField
        id="phoneNumber"
        label="Phone Number"
        value={phoneNumber}
        onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, "").slice(0, 11))}
        placeholder="Enter 11-digit number"
        helpText="Format: 11-digit number without spaces or dashes"
      />

      <div className="pt-4 w-fit text-white">
        <Button
          onClick={handleSendCall}
          disabled={isSendingCall}
          className="cursor-pointer hover:bg-black"
        >
          {isSendingCall ? "Calling..." : "Call Now"}
        </Button>
      </div>
    </div>
  );
}

interface FormFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder: string;
  helpText?: string;
}

function FormField({ 
  id, 
  label, 
  value, 
  onChange, 
  placeholder, 
  helpText 
}: FormFieldProps) {
  return (
    <div>
      <label htmlFor={id} className="block text-sm font-medium text-gray-300 mb-2">
        {label}
      </label>
      <Input
        type="text"
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
      {helpText && <p className="mt-1 text-xs text-gray-400">{helpText}</p>}
    </div>
  );
}