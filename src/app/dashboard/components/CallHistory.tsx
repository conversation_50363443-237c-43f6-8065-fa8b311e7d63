import Link from "next/link";
import { useState } from "react";
import { api } from "~/trpc/react";
import { Button } from "~/app/_components/ui/button";

type CallListItem = {
  id: string;
  userId: string;
  isSettled: boolean;
  createdAt: string | null;
  updatedAt: string | null;
}

export default function CallHistory({ userId }: { userId: string }) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Use tRPC query hook directly
  const { data: callHistory = [], isLoading } = api.sonexa.getCallsByUserId.useQuery(
      userId,
    {
      enabled: !!userId, // Only run query if userId exists
    }
  );

  // Pagination calculations
  const totalPages = Math.ceil(callHistory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCalls = callHistory.slice(startIndex, endIndex);

  return (
    <div className="text-center py-12">
      <h3 className="text-xl font-medium text-gray-400 mb-2">Call History</h3>

      {isLoading ? (
        <p className="text-blue-400">Loading call history...</p>
      ) : callHistory.length === 0 ? (
        <p className="text-gray-500">No calls made.</p>
      ) : (
        <div className="w-full">
          <PaginationInfo 
            startIndex={startIndex} 
            endIndex={endIndex} 
            totalItems={callHistory.length} 
          />
          <CallList calls={currentCalls} />
          <PaginationControls 
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
}

function PaginationInfo({ startIndex, endIndex, totalItems }: { 
  startIndex: number; 
  endIndex: number; 
  totalItems: number 
}) {
  return (
    <div className="mb-4">
      <p className="text-sm text-gray-400">
        Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} calls
      </p>
    </div>
  );
}

function CallList({ calls }: { calls: CallListItem[] }) {
  return (
    <ul className="space-y-2 text-left text-gray-300 w-full mx-auto mb-6">
      {calls.map((call, i) => (
        <Link href={`/calls/${call.id}`} key={call.id ?? i} className="bg-slate-900 px-4 py-2 rounded-lg border border-slate-600 w-full flex justify-evenly">
          <p className="text-xs text-gray-400 mt-1">
            {call.createdAt ? new Date(call.createdAt).toLocaleString() : 'N/A'}
          </p>
          <p><strong>Call ID:</strong> {call.id}</p>
          <p><strong>Status:</strong> {call.isSettled ? 'Settled' : 'In Progress'}</p>
          <p><strong>User:</strong> {call.userId}</p>
        </Link>
      ))}
    </ul>
  );
}

function PaginationControls({ 
  currentPage, 
  totalPages, 
  onPageChange 
}: { 
  currentPage: number; 
  totalPages: number; 
  onPageChange: (page: number) => void 
}) {
  if (totalPages <= 1) return null;
  
  const handlePreviousPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  return (
    <div className="flex items-center justify-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handlePreviousPage}
        disabled={currentPage === 1}
        className="text-gray-500 cursor-pointer hover:text-white border-slate-600 hover:bg-slate-700"
      >
        Previous
      </Button>

      <div className="flex space-x-1">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(page)}
            className={
              currentPage === page
                ? "bg-blue-500 text-white"
                : "text-gray-500 cursor-pointer hover:text-white border-slate-600 hover:bg-slate-700"
            }
          >
            {page}
          </Button>
        ))}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={handleNextPage}
        disabled={currentPage === totalPages}
        className="text-gray-600 border-slate-600 hover:bg-slate-700 hover:text-white cursor-pointer"
      >
        Next
      </Button>
    </div>
  );
}