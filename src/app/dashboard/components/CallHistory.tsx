import { useState, useEffect } from "react";
import { api } from "~/trpc/react";
import { Button } from "~/app/_components/ui/button";

type CallListItem = {
  c_id: string;
  created_at: string;
  call_length: number;
  to: string;
  completed: boolean;
  started_at: string;
  summary: string | null;
  call_ended_by: string | null;
}

export default function CallHistory({ userId }: { userId: string | undefined }) {
  const [isLoading, setIsLoading] = useState(false);
  const [callHistory, setCallHistory] = useState<CallListItem[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Pagination calculations
  const totalPages = Math.ceil(callHistory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCalls = callHistory.slice(startIndex, endIndex);



  // const getListCalls = api.bland.getListCalls.useMutation();


  useEffect(() => {
    fetchCallHistory();
  }, []);

  const fetchCallHistory = async () => {
    setIsLoading(true);
    try {
      const { data: calls, isLoading: callsLoading } = api.sonexa.getCallsByUserId.useQuery(userId ?? "");
      setCallHistory(calls ?? );
    } catch (error) {
      console.error("Error fetching call history:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="text-center py-12">
      <h3 className="text-xl font-medium text-gray-400 mb-2">Call History</h3>

      {isLoading ? (
        <p className="text-blue-400">Loading call history...</p>
      ) : callHistory.length === 0 ? (
        <p className="text-gray-500">No calls have been made yet.</p>
      ) : (
        <div className="w-full">
          <PaginationInfo 
            startIndex={startIndex} 
            endIndex={endIndex} 
            totalItems={callHistory.length} 
          />
          <CallList calls={currentCalls} />
          <PaginationControls 
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
}

function PaginationInfo({ startIndex, endIndex, totalItems }: { 
  startIndex: number; 
  endIndex: number; 
  totalItems: number 
}) {
  return (
    <div className="mb-4">
      <p className="text-sm text-gray-400">
        Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of {totalItems} calls
      </p>
    </div>
  );
}

function CallList({ calls }: { calls: CallListItem[] }) {
  return (
    <ul className="space-y-2 text-left text-gray-300 w-full mx-auto mb-6">
      {calls.map((call, i) => (
        <li key={call.c_id ?? i} className="bg-slate-900 px-4 py-2 rounded-lg border border-slate-600 w-full flex justify-evenly">
          <p className="text-xs text-gray-400 mt-1">{new Date(call.started_at).toLocaleString()}</p>
          <p><strong>Call ID:</strong> {call.c_id}</p>
          <p><strong>Number Contacted:</strong> {call.to ?? 'N/A'}</p>
        </li>
      ))}
    </ul>
  );
}

function PaginationControls({ 
  currentPage, 
  totalPages, 
  onPageChange 
}: { 
  currentPage: number; 
  totalPages: number; 
  onPageChange: (page: number) => void 
}) {
  if (totalPages <= 1) return null;
  
  const handlePreviousPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  return (
    <div className="flex items-center justify-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handlePreviousPage}
        disabled={currentPage === 1}
        className="text-gray-500 cursor-pointer hover:text-white border-slate-600 hover:bg-slate-700"
      >
        Previous
      </Button>

      <div className="flex space-x-1">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(page)}
            className={
              currentPage === page
                ? "bg-blue-500 text-white"
                : "text-gray-500 cursor-pointer hover:text-white border-slate-600 hover:bg-slate-700"
            }
          >
            {page}
          </Button>
        ))}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={handleNextPage}
        disabled={currentPage === totalPages}
        className="text-gray-600 border-slate-600 hover:bg-slate-700 hover:text-white cursor-pointer"
      >
        Next
      </Button>
    </div>
  );
}