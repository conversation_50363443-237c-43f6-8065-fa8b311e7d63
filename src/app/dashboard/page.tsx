import { useEffect, useState } from "react";
import DashboardHeader from "./_components/DashboardHeader";
import DashboardTabs from "./_components/DashboardTabs";
import CreateAgentForm from "./_components/CreateAgentForm";
import CallHistory from "./_components/CallHistory";
import AnalyticsPanel from "./_components/AnalyticsPanel";
import QuickTips from "./_components/QuickTips";
import { useSession } from "next-auth/react";
import Dashboard from "./_components/Dashboard";

export default function DashboardPage() {
  const session = useSession();

  const userId = session.data?.user.id;

  if (!userId) {
    return <div>No user ID founds</div>
  }
  
  return (
   <Dashboard userId={userId} />
  );
}
