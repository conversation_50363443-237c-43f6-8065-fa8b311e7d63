'use client';

import { useEffect, useState } from "react";
import DashboardHeader from "./components/DashboardHeader";
import DashboardTabs from "./components/DashboardTabs";
import CreateAgentForm from "./components/CreateAgentForm";
import CallHistory from "./components/CallHistory";
import AnalyticsPanel from "./components/AnalyticsPanel";
import QuickTips from "./components/QuickTips";
import { useSession } from "next-auth/react";

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("create");
  const session = useSession();

  const userId = session.data?.user.id;

  if (!userId) {
    return <div>No user ID founds</div>
  }
  
  return (
    <main className="min-h-screen bg-slate-900">
      <div className="container mx-auto px-4 py-8">
        <DashboardHeader />
        
        <div className="bg-slate-800 rounded-xl shadow-xl overflow-hidden mb-8">
          <DashboardTabs 
            activeTab={activeTab} 
            setActiveTab={setActiveTab} 
          />
          
          <div className="p-5">
            {activeTab === "create" && (
              <CreateAgentForm />
            )}

            {activeTab === "history" && (
              <CallHistory userId={userId} />
            )}

            {activeTab === "analytics" && (
              <AnalyticsPanel />
            )}
          </div>
        </div>
        
        <QuickTips />
      </div>
    </main>
  );
}
