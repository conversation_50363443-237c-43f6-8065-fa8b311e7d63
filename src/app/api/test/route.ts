// app/api/test-db/route.ts
import { db } from "~/server/db";
import { sql } from "drizzle-orm";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const result = await db.execute(
            sql`SELECT * FROM next_auth.user WHERE email = '<EMAIL>'`
        );

        return NextResponse.json(result);
    } catch (err) {
        console.error("Raw SQL DB query failed:", err);
        return new NextResponse("Internal Server Error", { status: 500 });
    }
}
