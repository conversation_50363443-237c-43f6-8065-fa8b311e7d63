import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from "@sentry/nextjs";
import { env } from "~/env";
import OpenAI from "openai";
import {
  buildAndReturnAiCallAnalysisPrompt,
  setCallToCompletedDb,
  storeCallAnalysisInDb,
  verifyWebhookSignature,
} from "~/lib/call-helpers";

const OpenAIClient = new OpenAI();

export async function POST(req: NextRequest) {
  try {
    const rawBody = await req.text();
    const signature = req.headers.get("x-webhook-signature") || "";

    const isValid = verifyWebhookSignature(env.WEBHOOK_SECRET, rawBody, signature);
    if (!isValid) {
      Sentry.captureException("Invalid signature");
      console.error("Invalid signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const parsedBody = JSON.parse(rawBody);

    // Build AI prompt
    const aiPromptForCallAnalysis = await buildAndReturnAiCallAnalysisPrompt(parsedBody.concatenated_transcript);

    // Call OpenAI
    const response = await OpenAIClient.responses.create({
      model: "gpt-4.1-nano",
      input: aiPromptForCallAnalysis,
    });

    const aiAnalysisResult = JSON.parse(response.output_text);
    console.log("Parsed AI analysis:", aiAnalysisResult);

    // Store result
    try {
      await storeCallAnalysisInDb(parsedBody.call_id, aiAnalysisResult);
    } catch (error) {
      console.error("DB store error", error);
      Sentry.captureException(error);
      throw new Error(`[webhook] Error storing analysis for call ${parsedBody.call_id}: ${error}`);
    }

    // Mark complete
    try {
      await setCallToCompletedDb(parsedBody.call_id);
    } catch (error) {
      console.error("Error setting call to completed", error);
      Sentry.captureException(error);
      throw new Error(`[webhook] Error setting call ${parsedBody.call_id} to settled: ${error}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    Sentry.captureException(error);
    console.error("Webhook handler error", error);
    return NextResponse.json(
      { error: "Unexpected server error", details: (error as Error).message },
      { status: 500 }
    );
  }
}
