// src/app/api/webhooks/bland/route.ts

import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { env } from "~/env";
import OpenAI from "openai";
const client = new OpenAI();

import { buildAndReturnAiCallAnalysisPrompt } from "~/lib/helpers";

function verifyWebhookSignature(key: string, data: string, signature: string) {
  const expectedSignature = crypto
      .createHmac('sha256', key)
      .update(data)
      .digest('hex');
  return expectedSignature === signature;
}

export async function POST(req: NextRequest) {

  const rawBody = await req.text();
  const signature = req.headers.get('x-webhook-signature') || '';

  const isValid = verifyWebhookSignature(
      env.WEBHOOK_SECRET,
      rawBody,
      signature
  );

  if (!isValid) {
    console.error("Invalid signature:");
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
  }

  const parsedBody = JSON.parse(rawBody);
  const callTranscript = parsedBody.concatenated_transcript;


  try {
    const aiPromptForCallAnalysis = await buildAndReturnAiCallAnalysisPrompt(callTranscript);

    const response = await client.responses.create({
      model: "gpt-4.1-nano",
      input: aiPromptForCallAnalysis,
    });

    const aiAnalysisResult = JSON.parse(response.output_text);

    //post analysis to supabase edge function
    // TODO rename this edge function
    const supabaseResponse = await fetch(`${env.SUPABASE_LOCAL_URL}/functions/v1/analyse-and-store-call`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.SUPABASE_LOCAL_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        analysis: aiAnalysisResult,
        transcript: callTranscript,
        call_id: parsedBody.call_id,
      }),
    });
    // update app state. ( call has completed - analyis ready )

    console.log(aiAnalysisResult);
    console.log("Supabase response:", supabaseResponse);

  } catch (error) {
    console.error("Error building AI prompt:", error);
    return NextResponse.json({ error: 'Failed to build AI prompt' }, { status: 500 });
  }


  console.log("Webhook received:", JSON.parse(rawBody));

  return NextResponse.json({ success: true })
}
