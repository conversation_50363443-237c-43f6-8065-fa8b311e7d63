// src/app/api/webhooks/bland/route.ts

import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from "@sentry/nextjs";
import { env } from "~/env";
import OpenAI from "openai";
import { buildAndReturnAiCallAnalysisPrompt, setCallToCompleted, storeCallAnalysisInDb } from "~/lib/call-helpers";
import { verifyWebhookSignature } from "~/lib/call-helpers";

const OpenAIClient = new OpenAI();

export async function POST(req: NextRequest) {
  const rawBody = await req.text();
  const signature = req.headers.get('x-webhook-signature') || '';

  const isValid = verifyWebhookSignature(
      env.WEBHOOK_SECRET,
      rawBody,
      signature
  );

  if (!isValid) {
    console.error("Invalid signature:");
    return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
  }

  const parsedBody = JSON.parse(rawBody);

  try {
    const aiPromptForCallAnalysis = await buildAndReturnAiCallAnalysisPrompt(parsedBody.concatenated_transcript);

    const response = await OpenAIClient.responses.create({
      model: "gpt-4.1-nano",
      input: aiPromptForCallAnalysis,
    });

    const aiAnalysisResult = JSON.parse(response.output_text);

    try {
      await storeCallAnalysisInDb(parsedBody.call_id, aiAnalysisResult);
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`[webhook] Error storing analysis for call ${parsedBody.call_id}: ${error}`);
    }

    // set call to settled in db
    try {
      await setCallToCompleted(parsedBody.call_id);
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`[webhook] Error setting call ${parsedBody.call_id} to settled: ${error}`);
    }

  } catch (error) {
    Sentry.captureException(error);
    return NextResponse.json({ error: 'Failed to build AI prompt' }, { status: 500 });
  }

  return NextResponse.json({ success: true })
}
