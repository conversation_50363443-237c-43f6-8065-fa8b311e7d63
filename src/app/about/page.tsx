
'use client';

import { motion } from "framer-motion";

export default function AboutPage() {
    // Animation variants
    const fadeInUp = {
        initial: { opacity: 0, y: 60 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6, ease: "easeOut" }
    };

    const staggerContainer = {
        animate: {
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const fadeInLeft = {
        initial: { opacity: 0, x: -60 },
        animate: { opacity: 1, x: 0 },
        transition: { duration: 0.6, ease: "easeOut" }
    };

    const fadeInRight = {
        initial: { opacity: 0, x: 60 },
        animate: { opacity: 1, x: 0 },
        transition: { duration: 0.6, ease: "easeOut" }
    };

    return (
        <main className="min-h-screen bg-gray-900 text-white">
            {/* Hero Section */}
            <section className="pt-32 pb-16 bg-gradient-to-b from-blue-900 to-gray-900">
                <div className="container mx-auto px-6 text-center">
                    <motion.h1
                        className="text-4xl md:text-5xl font-bold mb-6"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, ease: "easeOut" }}
                    >
                        About Sonexa AI
                    </motion.h1>
                    <motion.p
                        className="text-xl text-blue-200 max-w-3xl mx-auto"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                    >
                        A modern SaaS platform that lets businesses deploy intelligent phone agents to collect customer feedback
                        and automatically transform conversations into actionable data.
                    </motion.p>
                </div>
            </section>

            {/* What We Do Section */}
            <section className="py-16 bg-gray-900">
                <div className="container mx-auto px-6">
                    <div className="max-w-4xl mx-auto">
                        <motion.h2
                            className="text-3xl mb-8 text-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                        >
                            What We Do
                        </motion.h2>
                        <motion.p
                            className="text-lg text-gray-300 leading-relaxed mb-8"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
                        >
                            Sonexa AI is a cutting-edge platform that enables businesses to deploy intelligent phone agents
                            powered by Bland AI to collect customer feedback, post-delivery insights, and survey responses.
                            Our technology automatically transforms these conversations into structured, actionable data that
                            helps businesses make informed decisions.
                        </motion.p>
                        <motion.p
                            className="text-lg text-gray-300 leading-relaxed"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
                        >
                            By replacing traditional surveys with natural-sounding AI phone calls, we help businesses gather
                            authentic feedback at scale while eliminating the need for expensive manual call centers or
                            review processes.
                        </motion.p>
                    </div>
                </div>
            </section>

            {/* The Problem Section */}
            <section className="py-16 bg-black">
                <div className="container mx-auto px-6">
                    <div className="max-w-4xl mx-auto">
                        <h2 className="text-3xl mb-8 text-center">The Problem</h2>

                        <div className="grid md:grid-cols-2 gap-8">
                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                                <div className="flex items-start mb-4">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-red-900/30 text-red-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Low-Quality Feedback</h3>
                                        <p className="mt-2 text-gray-300">
                                            Businesses struggle to get authentic, rich feedback from customers that provides actionable insights.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                                <div className="flex items-start mb-4">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-red-900/30 text-red-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Survey Fatigue</h3>
                                        <p className="mt-2 text-gray-300">
                                            Post-purchase surveys get ignored. Email NPS feels robotic and impersonal.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                                <div className="flex items-start mb-4">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-red-900/30 text-red-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Resource Intensive</h3>
                                        <p className="mt-2 text-gray-300">
                                            Manual call reviews are slow, expensive, and inconsistent, limiting their scalability.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                                <div className="flex items-start mb-4">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-red-900/30 text-red-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Unstructured Data</h3>
                                        <p className="mt-2 text-gray-300">
                                            Data from phone calls is unstructured and unused, leaving valuable insights untapped.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* What This App Solves Section */}
            <section className="py-16 bg-black ">
                <div className="container mx-auto px-6">
                    <div className="max-w-4xl mx-auto">
                        <h2 className="text-3xl mb-8 text-center">What This App Solves</h2>

                        <div className="grid md:grid-cols-2 gap-8">
                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-blue-900/30 text-blue-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Human Conversations</h3>
                                        <p className="mt-2 text-gray-300">
                                            Automated customer calls that feel human and conversational, collecting deep voice-based feedback at scale.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-blue-900/30 text-blue-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Instant Analysis</h3>
                                        <p className="mt-2 text-gray-300">
                                            Instantly analyze transcripts for sentiment, topics, and urgency, turning raw speech into structured insights.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-blue-900/30 text-blue-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Visualized Feedback</h3>
                                        <p className="mt-2 text-gray-300">
                                            Feedback is searchable, visualized, and can be connected to your existing business systems.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
                                <div className="flex items-start">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-blue-900/30 text-blue-400">
                                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="ml-4">
                                        <h3 className="text-xl font-bold text-white">Self-Serve Platform</h3>
                                        <p className="mt-2 text-gray-300">
                                            No team required — everything is AI-powered and self-serve, with zero developer input needed.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Data Analytics & Insights Section */}
            <section className="py-16 bg-gray-900">
                <div className="container mx-auto px-6">
                    <div className="max-w-4xl mx-auto">
                        <motion.h2
                            className="text-3xl font-bold mb-8 text-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                        >
                            Transform Conversations into Powerful Analytics
                        </motion.h2>

                        <motion.div
                            className="mb-12"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
                        >
                            <p className="text-lg text-gray-300 leading-relaxed text-center mb-8">
                                Every phone call generates rich, structured data that can be visualized and analyzed to drive business decisions.
                                Our platform automatically converts voice conversations into comprehensive analytics dashboards.
                            </p>
                        </motion.div>

                        <motion.div
                            className="grid md:grid-cols-2 gap-8 mb-12"
                            variants={staggerContainer}
                            initial="initial"
                            whileInView="animate"
                            viewport={{ once: true, margin: "-100px" }}
                        >
                            <motion.div
                                className="bg-gradient-to-br from-blue-900/30 to-purple-900/30 p-6 rounded-lg border border-blue-800/30"
                                variants={fadeInUp}
                            >
                                <div className="flex items-center mb-4">
                                    <div className="bg-blue-900/50 p-3 rounded-lg mr-4">
                                        <svg className="h-6 w-6 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-xl font-bold text-white">Real-Time Dashboards</h3>
                                </div>
                                <p className="text-gray-300">
                                    Watch customer satisfaction trends, sentiment analysis, and call completion rates update in real-time as your AI agents complete calls.
                                </p>
                            </motion.div>

                            <motion.div
                                className="bg-gradient-to-br from-green-900/30 to-blue-900/30 p-6 rounded-lg border border-green-800/30"
                                variants={fadeInUp}
                            >
                                <div className="flex items-center mb-4">
                                    <div className="bg-green-900/50 p-3 rounded-lg mr-4">
                                        <svg className="h-6 w-6 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-xl font-bold text-white">Interactive Charts</h3>
                                </div>
                                <p className="text-gray-300">
                                    Generate beautiful line graphs, bar charts, and heat maps showing customer feedback patterns, peak call times, and response quality metrics.
                                </p>
                            </motion.div>

                            <motion.div
                                className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 p-6 rounded-lg border border-purple-800/30"
                                variants={fadeInUp}
                            >
                                <div className="flex items-center mb-4">
                                    <div className="bg-purple-900/50 p-3 rounded-lg mr-4">
                                        <svg className="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                    </div>
                                    <h3 className="text-xl font-bold text-white">Trend Analysis</h3>
                                </div>
                                <p className="text-gray-300">
                                    Identify patterns over time with automated trend detection, seasonal analysis, and predictive insights based on historical call data.
                                </p>
                            </motion.div>

                            <motion.div
                                className="bg-gradient-to-br from-orange-900/30 to-red-900/30 p-6 rounded-lg border border-orange-800/30"
                                variants={fadeInUp}
                            >
                                <div className="flex items-center mb-4">
                                    <div className="bg-orange-900/50 p-3 rounded-lg mr-4">
                                        <svg className="h-6 w-6 text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                                        </svg>
                                    </div>
                                    <h3 className="text-xl font-bold text-white">Custom Reports</h3>
                                </div>
                                <p className="text-gray-300">
                                    Export detailed reports with sentiment scores, keyword frequency, call duration analytics, and customer satisfaction metrics for stakeholder presentations.
                                </p>
                            </motion.div>
                        </motion.div>

                        <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 p-8 rounded-lg border border-blue-800/30 text-center">
                            <h3 className="text-2xl font-bold text-white mb-4">From Voice to Insights in Minutes</h3>
                            <p className="text-lg text-gray-300 mb-6">
                                Our AI automatically extracts key metrics from every conversation: sentiment scores, topic categorization,
                                urgency levels, and satisfaction ratings. No manual analysis required.
                            </p>
                            <div className="flex flex-wrap justify-center gap-4 text-sm">
                                <span className="bg-blue-900/50 px-4 py-2 rounded-full text-blue-200">Sentiment Analysis</span>
                                <span className="bg-green-900/50 px-4 py-2 rounded-full text-green-200">Topic Extraction</span>
                                <span className="bg-purple-900/50 px-4 py-2 rounded-full text-purple-200">Urgency Detection</span>
                                <span className="bg-orange-900/50 px-4 py-2 rounded-full text-orange-200">Satisfaction Scoring</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Why Calls Beat Emails Section */}
            <section className="py-16 bg-black">
                <div className="container mx-auto px-6">
                    <div className="max-w-4xl mx-auto">
                        <motion.h2
                            className="text-3xl font-bold mb-8 text-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                        >
                            Why Phone Calls Outperform Email Surveys
                        </motion.h2>

                        <motion.div
                            className="mb-12 text-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
                        >
                            <p className="text-lg text-gray-300 leading-relaxed">
                                Email surveys are dying. Phone calls are the future of customer feedback. Here&apos;s why our AI-powered approach
                                delivers dramatically better results than traditional email-based surveys.
                            </p>
                        </motion.div>

                        <motion.div
                            className="grid md:grid-cols-2 gap-8 mb-12"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            {/* Email Problems */}
                            <motion.div
                                className="space-y-6"
                                variants={fadeInLeft}
                                initial="initial"
                                whileInView="animate"
                                viewport={{ once: true, margin: "-100px" }}
                            >
                                <h3 className="text-2xl font-bold text-red-400 text-center mb-6">❌ Email Survey Problems</h3>

                                <div className="bg-red-900/20 border border-red-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-red-300 mb-2">Terrible Response Rates</h4>
                                    <p className="text-gray-300 text-sm">
                                        Email surveys typically see 2-5% response rates. Most customers ignore them completely or mark them as spam.
                                    </p>
                                </div>

                                <div className="bg-red-900/20 border border-red-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-red-300 mb-2">Shallow, Rushed Responses</h4>
                                    <p className="text-gray-300 text-sm">
                                        When customers do respond, they give minimal effort - clicking random ratings without thought or context.
                                    </p>
                                </div>

                                <div className="bg-red-900/20 border border-red-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-red-300 mb-2">No Follow-Up Questions</h4>
                                    <p className="text-gray-300 text-sm">
                                        Static forms can&apos;t adapt or ask clarifying questions when customers mention important issues.
                                    </p>
                                </div>

                                <div className="bg-red-900/20 border border-red-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-red-300 mb-2">Survey Fatigue</h4>
                                    <p className="text-gray-300 text-sm">
                                        Customers are overwhelmed by constant email surveys from every company they interact with.
                                    </p>
                                </div>
                            </motion.div>

                            {/* Phone Call Advantages */}
                            <motion.div
                                className="space-y-6"
                                variants={fadeInRight}
                                initial="initial"
                                whileInView="animate"
                                viewport={{ once: true, margin: "-100px" }}
                            >
                                <h3 className="text-2xl font-bold text-green-400 text-center mb-6">✅ AI Phone Call Advantages</h3>

                                <div className="bg-green-900/20 border border-green-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-green-300 mb-2">Higher Engagement Rates</h4>
                                    <p className="text-gray-300 text-sm">
                                        Phone calls feel personal and important. Customers are more likely to participate in a conversation than fill out a form.
                                    </p>
                                </div>

                                <div className="bg-green-900/20 border border-green-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-green-300 mb-2">Rich, Detailed Feedback</h4>
                                    <p className="text-gray-300 text-sm">
                                        Conversations naturally lead to deeper insights. Customers explain their reasoning and share stories you&apos;d never get in a form.
                                    </p>
                                </div>

                                <div className="bg-green-900/20 border border-green-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-green-300 mb-2">Dynamic Conversations</h4>
                                    <p className="text-gray-300 text-sm">
                                        AI agents can ask follow-up questions, clarify responses, and explore interesting topics that emerge during the call.
                                    </p>
                                </div>

                                <div className="bg-green-900/20 border border-green-800/30 p-6 rounded-lg">
                                    <h4 className="text-lg font-bold text-green-300 mb-2">Emotional Context</h4>
                                    <p className="text-gray-300 text-sm">
                                        Voice tone, pace, and emotion provide crucial context that text-based surveys completely miss.
                                    </p>
                                </div>
                            </motion.div>
                        </motion.div>

                        <motion.div
                            className="bg-gradient-to-r from-green-900/20 to-blue-900/20 p-8 rounded-lg border border-green-800/30 text-center"
                            initial={{ opacity: 0, y: 30 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
                        >
                            <h3 className="text-2xl font-bold text-white mb-4">The Numbers Don&apos;t Lie</h3>
                            <motion.div
                                className="grid md:grid-cols-3 gap-6 mb-6"
                                variants={staggerContainer}
                                initial="initial"
                                whileInView="animate"
                                viewport={{ once: true, margin: "-100px" }}
                            >
                                <motion.div variants={fadeInUp}>
                                    <div className="text-3xl font-bold text-green-400 mb-2">85%</div>
                                    <p className="text-gray-300 text-sm">Higher completion rates vs email surveys</p>
                                </motion.div>
                                <motion.div variants={fadeInUp}>
                                    <div className="text-3xl font-bold text-blue-400 mb-2">3x</div>
                                    <p className="text-gray-300 text-sm">More detailed feedback per response</p>
                                </motion.div>
                                <motion.div variants={fadeInUp}>
                                    <div className="text-3xl font-bold text-purple-400 mb-2">60%</div>
                                    <p className="text-gray-300 text-sm">Faster insights delivery</p>
                                </motion.div>
                            </motion.div>
                        </motion.div>
                    </div>
                </div>
            </section>

        </main>
    );
}