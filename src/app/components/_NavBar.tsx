"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { SignOutButton } from "./_SignOutButton";
import { useSession } from "next-auth/react"
import { motion } from "framer-motion";

export default function Navbar() {
    const [scrolled, setScrolled] = useState(false);
    const { data: session } = useSession()

    // console.log(session, "client session");

    // Handle scroll effect
    useEffect(() => {
        const handleScroll = () => {
            const isScrolled = window.scrollY > 10;
            if (isScrolled !== scrolled) {
                setScrolled(isScrolled);
            }
        };

        window.addEventListener("scroll", handleScroll);
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, [scrolled]);

    return (
        <motion.nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? "bg-slate-900/80 backdrop-blur-md py-5" : "bg-transparent py-6"}`}
            initial={{ y: -75 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.5 }}
        >
            <div className="container mx-auto px-6">
                <div className="flex items-center justify-between">
                    {/* Logo */}
                    <Link href="/" className="flex items-center">
                        <span className="text-2xl tracking-wide font-light text-white">Sonexa AI</span>
                    </Link>

                    {/* Navigation Links */}
                    <div className="hidden md:flex items-center space-x-8">
                        <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">
                            Pricing
                        </Link>
                        <Link href="/about" className="text-white/80 hover:text-white transition-colors">
                            About
                        </Link>

                        {session ? (
                            <>
                                <Link href="/dashboard" className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-500 transition-colors">
                                    Dashboard
                                </Link>
                                <SignOutButton />
                            </>
                        ) : (
                            <Link href="/login" className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-500 transition-colors">
                                Login
                            </Link>
                        )}
                    </div>

                    {/* Mobile Menu Button */}
                    <div className="md:hidden">
                        <button className="text-white focus:outline-none" aria-label="Toggle menu">
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </motion.nav>
    );
}