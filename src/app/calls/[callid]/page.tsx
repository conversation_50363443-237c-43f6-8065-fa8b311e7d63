
import { api } from "~/trpc/server";

interface CallPageProps {
    params: {
        callid: string;
    };
}

export default async function CallPage({ params }: CallPageProps) {
    const { callid } = await params;

    if (!callid) {
        return <div>No call ID provided</div>;
    }

    const callAnalysis = await api.sonexa.getCallAnalysis(callid);

    console.log('LOG-=-', callAnalysis);

    if (!callAnalysis) {
        return <div>Call not found</div>;
    }

    const getSentimentColor = (sentiment: string) => {
        switch (sentiment) {
            case "positive": return "bg-emerald-500 text-white";
            case "negative": return "bg-red-500 text-white";
            case "neutral": return "bg-amber-500 text-white";
            default: return "bg-gray-500 text-white";
        }
    };

    const getUrgencyColor = (urgency: string) => {
        switch (urgency) {
            case "high": return "bg-red-100 text-red-800 border-red-200";
            case "medium": return "bg-amber-100 text-amber-800 border-amber-200";
            case "low": return "bg-emerald-100 text-emerald-800 border-emerald-200";
            default: return "bg-gray-100 text-gray-800 border-gray-200";
        }
    };

    const getScoreColor = (score: number) => {
        if (score >= 8) return "text-emerald-600";
        if (score >= 6) return "text-amber-600";
        return "text-red-600";
    };

    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <main className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
            <div className="container mx-auto px-4 pt-28 pb-8 max-w-5xl">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-4xl text-white mb-2">Call Analysis</h1>
                    <p className="text-slate-300">Detailed insights from customer feedback call</p>
                </div>

                {/* Main Content Grid */}
                <div className="grid gap-6">
                    {/* Overview Card */}
                    <div className="bg-slate-800 rounded-2xl shadow-xl border border-slate-700 overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-2xl font-semibold">id: {callAnalysis.call_id}</h2>
                                    <p className="text-blue-100">Customer: {callAnalysis.user_id}</p>
                                </div>
                                <div className="text-right">
                                    <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold ${getSentimentColor(callAnalysis.call_sentiment)}`}>
                                        {callAnalysis.call_sentiment.charAt(0).toUpperCase() + callAnalysis.call_sentiment.slice(1)}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Satisfaction Score */}
                                <div className="text-center">
                                    <div className="mb-2">
                                        <span className={`text-4xl font-bold ${getScoreColor(callAnalysis.customer_satisfaction_score)}`}>
                                            {callAnalysis.customer_satisfaction_score}
                                        </span>
                                        <span className="text-2xl text-slate-400">/10</span>
                                    </div>
                                    <p className="text-slate-300 font-medium">Satisfaction Score</p>
                                </div>

                                {/* Urgency */}
                                <div className="text-center">
                                    <div className={`inline-flex items-center px-4 py-2 rounded-lg border-2 font-semibold ${getUrgencyColor(callAnalysis.urgency)}`}>
                                        {callAnalysis.urgency.charAt(0).toUpperCase() + callAnalysis.urgency.slice(1)} Priority
                                    </div>
                                    <p className="text-slate-300 font-medium mt-2">Urgency Level</p>
                                </div>

                                {/* Feedback Type */}
                                <div className="text-center">
                                    <div className="flex justify-center gap-2 mb-2">
                                        {callAnalysis.complaint && (
                                            <span className="bg-red-900/50 text-red-300 px-3 py-1 rounded-full text-sm font-medium border border-red-700">
                                                📢 Complaint
                                            </span>
                                        )}
                                        {callAnalysis.compliment && (
                                            <span className="bg-emerald-900/50 text-emerald-300 px-3 py-1 rounded-full text-sm font-medium border border-emerald-700">
                                                👏 Compliment
                                            </span>
                                        )}
                                        {!callAnalysis.complaint && !callAnalysis.compliment && (
                                            <span className="bg-slate-700 text-slate-300 px-3 py-1 rounded-full text-sm font-medium border border-slate-600">
                                                💬 General Feedback
                                            </span>
                                        )}
                                    </div>
                                    <p className="text-slate-300 font-medium">Feedback Type</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Products & Topics Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Products Mentioned */}
                        <div className="bg-slate-800 rounded-2xl shadow-xl border border-slate-700 p-6">
                            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                                🛁 Products Mentioned
                            </h3>
                            {callAnalysis.products_mentioned && callAnalysis.products_mentioned.length > 0 ? (
                                <div className="space-y-2">
                                    {callAnalysis.products_mentioned.map((product, index) => (
                                        <div key={index} className="bg-blue-900/30 border border-blue-700 rounded-lg p-3">
                                            <span className="text-blue-300 font-medium">{product}</span>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-slate-400 italic">No specific products mentioned</p>
                            )}
                        </div>

                        {/* Topics */}
                        <div className="bg-slate-800 rounded-2xl shadow-xl border border-slate-700 p-6">
                            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                                🏷️ Discussion Topics
                            </h3>
                            {callAnalysis.topics && callAnalysis.topics.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                    {callAnalysis.topics.map((topic, index) => (
                                        <span key={index} className="bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm font-medium border border-purple-700">
                                            {topic.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </span>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-slate-400 italic">No topics identified</p>
                            )}
                        </div>
                    </div>

                    {/* Actionable Suggestions */}
                    <div className="bg-slate-800 rounded-2xl shadow-xl border border-slate-700 p-6">
                        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                            💡 Actionable Suggestions
                        </h3>
                        {callAnalysis.actionable_suggestions && callAnalysis.actionable_suggestions.length > 0 ? (
                            <div className="space-y-3">
                                {callAnalysis.actionable_suggestions.map((suggestion, index) => (
                                    <div key={index} className="flex items-start gap-3 p-4 bg-gradient-to-r from-emerald-900/30 to-blue-900/30 rounded-lg border border-emerald-700/50">
                                        <div className="bg-emerald-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                                            {index + 1}
                                        </div>
                                        <p className="text-slate-200 font-medium">{suggestion}</p>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="text-slate-400 italic">No actionable suggestions identified</p>
                        )}
                    </div>

                    {/* Metadata */}
                    <div className="bg-slate-700 rounded-2xl border border-slate-600 p-6">
                        <h3 className="text-lg font-bold text-white mb-4">Call Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-slate-300">Call ID:</span>
                                <span className="ml-2 font-mono bg-slate-600 text-slate-200 px-2 py-1 rounded">{callAnalysis.call_id}</span>
                            </div>
                            <div>
                                <span className="text-slate-300">Analysis ID:</span>
                                <span className="ml-2 font-mono bg-slate-600 text-slate-200 px-2 py-1 rounded">{callAnalysis.id}</span>
                            </div>
                            <div>
                                <span className="text-slate-300">Created:</span>
                                <span className="ml-2 text-slate-200">{formatDate(callAnalysis.created_at)}</span>
                            </div>
                            <div>
                                <span className="text-slate-300">Last Updated:</span>
                                <span className="ml-2 text-slate-200">{formatDate(callAnalysis.updated_at)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    );
}