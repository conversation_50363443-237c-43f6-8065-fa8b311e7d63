'use client';

import { api } from "~/trpc/";
import { useParams } from "next/navigation";

export default async function CallPage() {
    const { callid } = useParams();

    console.log(callid);

    if (!callid) {
        return <div>No call ID provided</div>;
    }

    const callAnalysis = await api.sonexa.getCallAnalysis(callid as string);

    console.log(callAnalysis);

    if (!callAnalysis) {
        return <div>Call not found</div>;
    }



    return (
        <main className="min-h-screen bg-slate-900">
            <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-8 text-center">Call Details</h1>
            <div className="bg-slate-800 rounded-xl shadow-xl overflow-hidden mb-8">
                <div className="p-5">
                {/* Call details will go here */}
                <p>HELLO</p>
                </div>
            </div>
            </div>
        </main>
    );
}