// ~/hooks/useRealtimeCalls.ts
import { useEffect } from "react";
import { supabase } from "~/lib/supabase-client";

/**
 * Subscribe to realtime call updates and trigger a callback when a change occurs.
 */
export function useRealtimeCallStatus(onUpdate: () => void) {
  useEffect(() => {
    const channel = supabase
      .channel("realtime_calls")
      .on(
        "postgres_changes",
        { event: "UPDATE", schema: "public", table: "calls" },
        () => {
          onUpdate();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [onUpdate]);
}
