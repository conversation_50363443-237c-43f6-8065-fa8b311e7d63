import { useState } from 'react';
import { setCallToCompleted } from '~/lib/call-helpers';
import { api } from "~/trpc/react";

export function useSendCall() {
    const [agentName, setAgentName] = useState("");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [callStatus, setCallStatus] = useState<"idle" | "success" | "error">("idle");
    const [task, setTask] = useState("");
    const [call_id, setCallId] = useState<string | null>(null);

    const { mutateAsync: sendCall, isPending: isSendingCall } = api.bland.sendCall.useMutation();
    const { mutateAsync: storeCallRecord, isPending: isStoringCall } = api.sonexa.storeCallRecord.useMutation();

    const handleSendCall = async () => {
        // Validation
        if (!agentName.trim()) {
            alert("Please enter an agent name");
            return;
        }

        if (!task.trim()) {
            alert("Please fill in a task description");
            return;
        }

        const phoneRegex = /^\d{11}$/;
        if (!phoneRegex.exec(phoneNumber)) {
            alert("Please enter a valid 11-digit phone number");
            return;
        }

        // Validate environment variable
        const ngrokUrl = process.env.NEXT_PUBLIC_NGROK_URL;
        if (!ngrokUrl) {
            alert("NGROK URL is not configured. Please check your environment variables.");
            return;
        }

        const callData = {
            phone_number: `+44${phoneNumber}`,
            task: task,
            wait_for_greeting: true,
            temperature: 0.5,
            local_dialing: true,
            noise_cancellation: true,
            ignore_button_press: true,
            max_duration: 10,
            voice: "bb88042c-7858-4875-a686-8e193414ded5",
            webhook: `${ngrokUrl}/functions/v1/analyse-and-store-call`,
        };

        try {
            const response = await sendCall(callData);
            const newCallId = response.call_id;
            setCallToInProgress(newCallId);
            setCallId(newCallId);

            await storeCallRecord(newCallId);
            setCallStatus("success");
        } catch (error) {
            setCallStatus("error");
            console.error("Error sending call:", error);
        }
    };

    return {
        handleSendCall,
        isSendingCall,
        isStoringCall,
        callStatus,
        setAgentName,
        agentName,
        phoneNumber,
        setPhoneNumber,
        setTask,
        task,
    };
}
