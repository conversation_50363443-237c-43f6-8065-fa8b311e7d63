import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { TRPCReactProvider } from "~/trpc/react";
import Navbar from "~/app/_components/_NavBar";
import { SessionProvider } from "next-auth/react";
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import Footer from "./_components/_Footer";

export const metadata: Metadata = {
  title: "Sonexa AI - Automated Customer Feedback",
  description: "Create AI phone agents that call your customers, collect feedback, and deliver actionable insights.",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default async function RootLayout({children,} : Readonly<{ children: React.ReactNode }>) {

  return (
      <html lang="en" className={`${geist.variable}`}>
      <body>
          <TRPCReactProvider>
              <SessionProvider>
                  <ReactQueryDevtools initialIsOpen={false} />
                <Navbar />
                {children}
                <Footer />
              </SessionProvider>
          </TRPCReactProvider>
      </body>
      </html>
  );
}