'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useSession } from "next-auth/react";

export default function Home() {
  const { data: session } = useSession();

  return (
        <main className="min-h-screen bg-gray-900">
          {/* Hero Section */}
          <section className="bg-gradient-to-r from-gray-800 to-blue-900 text-gray-100 lg:pt-10">
            <div className="container mx-auto pl-8 pr-2 py-20">
              <div className="flex flex-col items-center md:flex-row">
                <motion.div
                  className="md:w-1/2"
                  initial={{ opacity: 0, x: -60 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                >
                  <motion.h1
                    className="mb-6 text-4xl font-bold leading-tight md:text-5xl"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                  >
                    Automate Customer Feedback Calls with AI
                  </motion.h1>
                  <motion.p
                    className="mb-8 text-xl text-gray-300"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                  >
                    Create AI phone agents that call your customers, collect feedback, and deliver actionable insights.
                  </motion.p>
                  <motion.div
                    className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-yf-0"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
                  >
                    <Link
                        href={session ? "/dashboard" : "/login"}
                        className="rounded-lg bg-blue-600 px-8 py-3 text-center font-semibold text-white shadow-lg transition hover:bg-blue-500"
                    >
                      {session ? "Go to Dashboard" : "Get Started"}
                    </Link>
                    <Link
                        href="/demo"
                        className="rounded-lg border border-blue-400 bg-transparent px-8 py-3 text-center font-semibold text-blue-400 transition hover:bg-blue-900/30"
                    >
                      See Demo
                    </Link>
                  </motion.div>
                </motion.div>
                <motion.div
                  className="mt-10 md:mt-0 md:w-1/2"
                  initial={{ opacity: 0, x: 60 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
                >
                  <img
                      src="/images/sonexa-icon.png"
                      alt="AI Phone Agent Illustration"
                      className="mx-auto w-full max-w-md"
                  />
                </motion.div>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-20 bg-black">
            <div className="container mx-auto px-6">
              <h2 className="mb-12 text-center text-3xl font-bold text-white md:text-4xl">
                Why Choose Our Platform
              </h2>
              <div className="grid gap-8 md:grid-cols-3">
                <div className="rounded-lg bg-gray-700 p-6 shadow-lg border border-gray-600">
                  <div className="mb-4 rounded-full w-fit bg-blue-900 p-3 text-blue-300">
                    <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                    </svg>
                  </div>
                  <h3 className="mb-2 text-xl font-bold text-white">Higher Response Rates</h3>
                  <p className="text-gray-300">
                    Voice calls feel more personal and get 3-5x higher response rates than emails or SMS surveys.
                  </p>
                </div>
                <div className="rounded-lg bg-gray-700 p-6 shadow-lg border border-gray-600">
                  <div className="mb-4 rounded-full w-fit bg-blue-900 p-3 text-blue-300">
                    <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"></path>
                    </svg>
                  </div>
                  <h3 className="mb-2 text-xl font-bold text-white">Scale Without Hiring</h3>
                  <p className="text-gray-300">
                    Make hundreds of feedback calls simultaneously without the cost of a traditional call center.
                  </p>
                </div>
                <div className="rounded-lg bg-gray-700 p-6 shadow-lg border border-gray-600">
                  <div className="mb-4 rounded-full w-fit bg-blue-900 p-3 text-blue-300">
                    <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                      <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                    </svg>
                  </div>
                  <h3 className="mb-2 text-xl font-bold text-white">Actionable Insights</h3>
                  <p className="text-gray-300">
                    Turn open-ended conversations into structured data and actionable business insights.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* How It Works */}
          <section className="py-20 bg-gray-900">
            <div className="container mx-auto px-6">
              <motion.h2
                className="mb-12 text-center text-3xl font-bold text-white md:text-4xl"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                How It Works
              </motion.h2>
              <motion.div
                className="grid gap-8 md:grid-cols-4"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.8, staggerChildren: 0.2 }}
              >
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-2xl font-bold text-white">1</div>
                  <h3 className="mb-2 text-xl font-bold text-white">Design Your Agent</h3>
                  <p className="text-gray-300">Create your AI phone agent and design your survey questions</p>
                </motion.div>
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-2xl font-bold text-white">2</div>
                  <h3 className="mb-2 text-xl font-bold text-white">Upload Contacts</h3>
                  <p className="text-gray-300">Import your customer list or select specific segments</p>
                </motion.div>
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-2xl font-bold text-white">3</div>
                  <h3 className="mb-2 text-xl font-bold text-white">Launch Campaign</h3>
                  <p className="text-gray-300">Schedule calls and let our AI agents collect feedback</p>
                </motion.div>
                <motion.div
                  className="text-center"
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
                >
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-2xl font-bold text-white">4</div>
                  <h3 className="mb-2 text-xl font-bold text-white">Analyze Results</h3>
                  <p className="text-gray-300">Review insights and take action based on customer feedback</p>
                </motion.div>
              </motion.div>
            </div>
          </section>

          {/* AI Insights Section - Replacing Testimonials */}
          <section className="py-20 bg-black">
            <div className="container mx-auto px-6">
              <h2 className="mb-12 text-center text-3xl font-bold text-white md:text-4xl">
                AI-Powered Insights from Every Call
              </h2>
              <div className="flex flex-col md:flex-row items-center">
                <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                  <img
                      src="/images/ai-assistance.png"
                      alt="AI Insights Dashboard"
                      className="w-full max-w-lg mx-auto rounded-lg shadow-xl border border-gray-600"
                  />
                </div>
                <div className="md:w-1/2">
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        <div className="flex items-center justify-center h-10 w-10 rounded-md bg-blue-600 text-white">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-xl font-bold text-white">Sentiment Analysis</h3>
                        <p className="mt-2 text-gray-300">Our AI automatically detects customer sentiment in each conversation, helping you identify satisfied customers and those who need attention.</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        <div className="flex items-center justify-center h-10 w-10 rounded-md bg-blue-600 text-white">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-xl font-bold text-white">Topic Extraction</h3>
                        <p className="mt-2 text-gray-300">Identify common themes and topics from open-ended conversations, automatically categorizing feedback without manual review.</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        <div className="flex items-center justify-center h-10 w-10 rounded-md bg-blue-600 text-white">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-xl font-bold text-white">Actionable Recommendations</h3>
                        <p className="mt-2 text-gray-300">Get AI-generated recommendations based on customer feedback patterns, helping you prioritize improvements with the highest impact.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Data Visualization Section */}
          <section className="py-20 bg-gray-900">
            <div className="container mx-auto px-6">
              <h2 className="mb-12 text-center text-3xl font-bold text-white md:text-4xl">
                Turn Conversations into Visual Insights
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Chart 1: Customer Satisfaction */}
                <div className="bg-black rounded-lg shadow-xl border border-gray-700 overflow-hidden">
                  <div className="p-5 border-b border-gray-700">
                    <h3 className="font-bold text-xl text-white">Customer Satisfaction Trends</h3>
                    <p className="text-gray-400 mt-1">Track happiness scores over time</p>
                  </div>
                  <div className="p-5">
                    <img
                        src="/images/improving.png"
                        alt="Customer Satisfaction Chart"
                        className="w-full h-48 object-contain"
                    />
                    <div className="mt-4 flex items-center justify-between">
                      <div>
                        <span className="text-emerald-400 font-bold text-xl">+12%</span>
                        <span className="text-gray-400 ml-1 text-sm">vs last month</span>
                      </div>
                      <span className="bg-emerald-900/50 text-emerald-400 text-xs px-2 py-1 rounded-full font-medium">
                      Improving
                    </span>
                    </div>
                  </div>
                </div>

                {/* Chart 2: Common Topics */}
                <div className="bg-black rounded-lg shadow-xl border border-gray-700 overflow-hidden">
                  <div className="p-5 border-b border-gray-700">
                    <h3 className="font-bold text-xl text-white">Top Feedback Topics</h3>
                    <p className="text-gray-400 mt-1">What customers talk about most</p>
                  </div>
                  <div className="p-5">
                    <img
                        src="/images/feedback.png"
                        alt="Feedback Topics Chart"
                        className="w-full h-48 object-contain"
                    />
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                        <span className="text-gray-200">Delivery Speed (42%)</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-violet-400 mr-2"></div>
                        <span className="text-gray-200">Product Quality (27%)</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-sky-400 mr-2"></div>
                        <span className="text-gray-200">Customer Service (18%)</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Chart 3: Sentiment Analysis */}
                <div className="bg-black rounded-lg shadow-xl border border-gray-700 overflow-hidden">
                  <div className="p-5 border-b border-gray-700">
                    <h3 className="font-bold text-xl text-white">Sentiment Distribution</h3>
                    <p className="text-gray-400 mt-1">Emotional tone of customer feedback</p>
                  </div>
                  <div className="p-5">
                    <img
                        src="/images/sentiment.png"
                        alt="Sentiment Analysis Chart"
                        className="w-full h-48 object-contain"
                    />
                    <div className="mt-4 grid grid-cols-3 gap-2">
                      <div className="text-center">
                        <div className="text-emerald-400 font-bold text-xl">68%</div>
                        <div className="text-xs text-gray-400">Positive</div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-300 font-bold text-xl">22%</div>
                        <div className="text-xs text-gray-400">Neutral</div>
                      </div>
                      <div className="text-center">
                        <div className="text-rose-400 font-bold text-xl">10%</div>
                        <div className="text-xs text-gray-400">Negative</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-12 text-center">
                <p className="text-gray-400 max-w-2xl mx-auto mb-6">
                  All charts update in real-time as new feedback comes in. Export reports or set up automated alerts when metrics change significantly.
                </p>
                <Link
                    href="/demo-dashboard"
                    className="inline-block rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white shadow-md transition hover:bg-blue-500"
                >
                  Explore Interactive Demo Dashboard
                </Link>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="bg-gray-800 py-20 text-white">
            <div className="container mx-auto px-6 text-center">
              <h2 className="mb-6 text-3xl font-bold md:text-4xl">
                Ready to transform your customer feedback process?
              </h2>
              <p className="mb-8 text-xl text-gray-300">
                Join hundreds of businesses already using our platform to collect better feedback at scale.
              </p>
              <Link
                  href={session ? "/dashboard" : "/login"}
                  className="inline-block rounded-lg bg-blue-600 px-8 py-3 font-semibold text-white shadow-lg transition hover:bg-blue-500"
              >
                {session ? "Go to Dashboard" : "Start Free Trial"}
              </Link>
            </div>
          </section>

        </main>
  );
}