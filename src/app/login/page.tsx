
'use client';

import { motion } from "framer-motion";
import { useSession } from "next-auth/react";
import { SignInButton } from "~/app/_components/_SignInButton";
import { SignOutButton } from "~/app/_components/_SignOutButton";
import Image from 'next/image';

export default function LoginPage() {
  const { data: session } = useSession();

  return (
      <main className="min-h-screen bg-gradient-to-r from-slate-900 to-blue-900 flex items-center justify-center px-4 py-12">
        <motion.div
          className="max-w-md w-full bg-white rounded-xl shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 50, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <motion.div
            className="p-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            >
              <h1 className="text-3xl font-bold text-slate-800">
                {session ? 'Welcome Back' : 'Sign In'}
              </h1>
              <p className="text-slate-500 mt-2">
                {session
                  ? 'You are currently signed in to your account'
                  : 'Sign in to access your dashboard and insights'}
              </p>
            </motion.div>

            {session ? (
              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              >
                <div className="bg-slate-50 rounded-lg p-4 flex items-center">
                  <div className="flex-shrink-0 mr-3">
                    {session.user?.image ? (
                      <Image
                        width={500}
                        height={500}
                        src={session.user.image}
                        alt={session.user.name ?? 'User'}
                        className="h-12 w-12 rounded-full"
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-lg">
                          {session.user?.name?.charAt(0) ?? 'U'}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-slate-700">{session.user?.name}</p>
                    <p className="text-sm text-slate-500">{session.user?.email}</p>
                  </div>
                </div>

                <motion.div
                  className="flex flex-col gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                >
                  <a
                    href="/dashboard"
                    className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg text-center transition-colors"
                  >
                    Go to Dashboard
                  </a>
                  <SignOutButton />
                </motion.div>
              </motion.div>
            ) : (
              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              >
                <div className="bg-slate-50 rounded-lg p-4">
                  <p className="text-slate-600 text-sm">
                    Sign in with your Google account to access all features. We use Google for secure authentication.
                  </p>
                </div>

                <motion.div
                  className="flex flex-col gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                >
                  <SignInButton />
                  <p className="text-xs text-center text-slate-500 mt-2">
                    By signing in, you agree to our Terms of Service and Privacy Policy
                  </p>
                </motion.div>
              </motion.div>
            )}
          </motion.div>

          <motion.div
            className="px-8 py-4 bg-slate-50 border-t border-slate-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
          >
            <p className="text-sm text-slate-500 text-center">
              Need help? <a href="/contact" className="text-blue-600 hover:text-blue-800">Contact support</a>
            </p>
          </motion.div>
        </motion.div>
      </main>
  );
}