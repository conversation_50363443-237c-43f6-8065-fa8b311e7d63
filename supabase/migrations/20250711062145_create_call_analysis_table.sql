-- Create call_analysis table for storing customer feedback analysis
CREATE TABLE call_analysis (
    id BIGSERIAL PRIMARY KEY,
    call_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    call_sentiment TEXT NOT NULL CHECK (call_sentiment IN ('positive', 'neutral', 'negative')),
    complaint BOOLEAN NOT NULL DEFAULT false,
    compliment BOOLEAN NOT NULL DEFAULT false,
    products_mentioned JSONB NOT NULL DEFAULT '[]'::jsonb,
    actionable_suggestions JSONB NOT NULL DEFAULT '[]'::jsonb,
    urgency TEXT NOT NULL CHECK (urgency IN ('low', 'medium', 'high')),
    topics JSONB NOT NULL DEFAULT '[]'::jsonb,
    customer_satisfaction_score INTEGER NOT NULL CHECK (customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for better query performance
CREATE INDEX idx_call_analysis_call_id ON call_analysis(call_id);
CREATE INDEX idx_call_analysis_user_id ON call_analysis(user_id);
CREATE INDEX idx_call_analysis_sentiment ON call_analysis(call_sentiment);
CREATE INDEX idx_call_analysis_urgency ON call_analysis(urgency);
CREATE INDEX idx_call_analysis_satisfaction_score ON call_analysis(customer_satisfaction_score);
CREATE INDEX idx_call_analysis_created_at ON call_analysis(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on row updates
CREATE TRIGGER update_call_analysis_updated_at
    BEFORE UPDATE ON call_analysis
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();