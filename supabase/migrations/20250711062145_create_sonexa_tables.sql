-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create calls table to track call state
CREATE TABLE calls (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('new', 'in_progress', 'completed', 'failed', 'cancelled')) DEFAULT 'new',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for calls table
CREATE INDEX idx_calls_user_id ON calls(user_id);
CREATE INDEX idx_calls_status ON calls(status);
CREATE INDEX idx_calls_created_at ON calls(created_at);

-- Create call_analysis table for storing customer feedback analysis
CREATE TABLE call_analysis (
    id BIGSERIAL PRIMARY KEY,
    call_id UUID NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    call_summary TEXT,
    call_sentiment TEXT CHECK (call_sentiment IN ('positive', 'neutral', 'negative')),
    complaint BOOLEAN NOT NULL DEFAULT false,
    compliment BOOLEAN NOT NULL DEFAULT false,
    products_mentioned JSONB DEFAULT '[]'::jsonb,
    actionable_suggestions JSONB DEFAULT '[]'::jsonb,
    urgency TEXT CHECK (urgency IN ('low', 'medium', 'high')),
    topics JSONB DEFAULT '[]'::jsonb,
    customer_satisfaction_score INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for call_analysis table
CREATE INDEX idx_call_analysis_call_id ON call_analysis(call_id);
CREATE INDEX idx_call_analysis_user_id ON call_analysis(user_id);
CREATE INDEX idx_call_analysis_sentiment ON call_analysis(call_sentiment);
CREATE INDEX idx_call_analysis_urgency ON call_analysis(urgency);
CREATE INDEX idx_call_analysis_satisfaction_score ON call_analysis(customer_satisfaction_score);
CREATE INDEX idx_call_analysis_created_at ON call_analysis(created_at);

-- Function to auto-update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Triggers
CREATE TRIGGER update_calls_updated_at
BEFORE UPDATE ON calls
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_analysis_updated_at
BEFORE UPDATE ON call_analysis
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
