import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "npm:@supabase/supabase-js@2";

// Types for better type safety
interface CallAnalysis {
  sentiment: string;
  complaint: boolean;
  compliment: boolean;
  urgency: string;
  topics: string[];
  customer_satisfaction_score: number;
}

interface RequestData {
  analysis: CallAnalysis;
  transcript: string;
  call_id: string;
}

interface CallAnalysisRecord {
  callid: string;
  userid: string;
  call_sentiment: string;
  complaint: boolean;
  compliment: boolean;
  products_mentioned: string[];
  actionable_suggestions: string[];
  urgency: string;
  topics: string[];
  customer_satisfaction_score: number;
}

// Error response helper
function createErrorResponse(message: string, status: number = 400) {
  return new Response(
    JSON.stringify({
      error: message,
      success: false
    }),
    {
      status,
      headers: { "Content-Type": "application/json" },
    }
  );
}

// Success response helper
function createSuccessResponse(data: any, status: number = 200) {
  return new Response(
    JSON.stringify({
      ...data,
      success: true
    }),
    {
      status,
      headers: { "Content-Type": "application/json" },
    }
  );
}

// Validation function
function validateRequestData(data: any): data is RequestData {
  if (!data) {
    throw new Error("Request body is required");
  }

  if (!data.call_id || typeof data.call_id !== 'string') {
    throw new Error("call_id is required and must be a string");
  }

  if (!data.analysis || typeof data.analysis !== 'object') {
    throw new Error("analysis is required and must be an object");
  }

  if (!data.transcript || typeof data.transcript !== 'string') {
    throw new Error("transcript is required and must be a string");
  }

  const { analysis } = data;

  if (typeof analysis.sentiment !== 'string') {
    throw new Error("analysis.sentiment is required and must be a string");
  }

  if (typeof analysis.complaint !== 'boolean') {
    throw new Error("analysis.complaint is required and must be a boolean");
  }

  if (typeof analysis.compliment !== 'boolean') {
    throw new Error("analysis.compliment is required and must be a boolean");
  }

  if (typeof analysis.urgency !== 'string') {
    throw new Error("analysis.urgency is required and must be a string");
  }

  if (!Array.isArray(analysis.topics)) {
    throw new Error("analysis.topics is required and must be an array");
  }

  if (typeof analysis.customer_satisfaction_score !== 'number') {
    throw new Error("analysis.customer_satisfaction_score is required and must be a number");
  }

  return true;
}

// Initialize Supabase client
function createSupabaseClient() {
  const url = Deno.env.get("LOCAL_URL");
  const key = Deno.env.get("LOCAL_ANON_KEY");

  if (!url || !key) {
    throw new Error("Missing required environment variables: LOCAL_URL and LOCAL_ANON_KEY");
  }

  return createClient(url, key);
}

// Insert call analysis into database
async function insertCallAnalysis(
  supabase: any,
  data: RequestData
): Promise<void> {
  const record: CallAnalysisRecord = {
    callid: data.call_id,
    userid: "1234567890", // TODO: Get from authenticated user
    call_sentiment: data.analysis.sentiment,
    complaint: data.analysis.complaint,
    compliment: data.analysis.compliment,
    products_mentioned: [], // TODO: Extract from analysis
    actionable_suggestions: [], // TODO: Extract from analysis
    urgency: data.analysis.urgency,
    topics: data.analysis.topics,
    customer_satisfaction_score: data.analysis.customer_satisfaction_score,
  };

  const { error } = await supabase
    .from("call_analysis")
    .insert(record);

  if (error) {
    console.error("Database insert error:", error);
    throw new Error(`Failed to insert call analysis: ${error.message}`);
  }

  console.log("Analysis successfully inserted for call:", data.call_id);
}

// Main handler function
async function handleRequest(req: Request): Promise<Response> {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return createErrorResponse("Method not allowed", 405);
    }

    // Parse and validate request data
    let requestData: any;
    try {
      requestData = await req.json();
    } catch (error) {
      return createErrorResponse("Invalid JSON in request body");
    }

    console.log("Received request data:", requestData);

    // Validate request data structure
    try {
      validateRequestData(requestData);
    } catch (error) {
      return createErrorResponse(`Validation error: ${error.message}`);
    }

    // Initialize Supabase client
    const supabase = createSupabaseClient();

    // Insert call analysis
    await insertCallAnalysis(supabase, requestData);

    // Return success response
    return createSuccessResponse({
      message: "Call analysis stored successfully",
      call_id: requestData.call_id
    });

  } catch (error) {
    console.error("Unexpected error:", error);
    return createErrorResponse(
      "Internal server error occurred",
      500
    );
  }
}

// Start the server
Deno.serve(handleRequest);
