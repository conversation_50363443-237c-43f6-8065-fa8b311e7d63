import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "npm:@supabase/supabase-js@2";

Deno.serve(async (req) => {
  const supabase = createClient(
    Deno.env.get("LOCAL_URL"),
    Deno.env.get("LOCAL_ANON_KEY")
  );

  const data = await req.json();
  const { analysis, transcript, call_id } = data;

  console.log("data:", data);

  try {
    const { data: analysisData, error: analysisError } = await supabase
      .from("call_analysis")
      .insert({
        callId: call_id,
        userId: "1234567890",
        call_sentiment: analysis.sentiment,
        complaint: analysis.complaint,
        compliment: analysis.compliment,
        products_mentioned: ["piss flaps"],
        actionable_suggestions: ["piss flaps"],
        urgency: analysis.urgency,
        topics: analysis.topics,
        customer_satisfaction_score: analysis.customer_satisfaction_score,
      });

    if (analysisError) {
      console.error("Supabase insert error:", analysisError);
    } else {
      console.log("Analysis inserted:", analysisData);
    }
  } catch (error) {
    console.error("Unexpected error inserting analysis:", error);
  }

  return new Response(JSON.stringify({ records: data }), {
    headers: { "Content-Type": "application/json" },
  });
});
