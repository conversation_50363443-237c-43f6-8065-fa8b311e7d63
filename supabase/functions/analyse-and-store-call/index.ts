import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "npm:@supabase/supabase-js@2";

Deno.serve(async (req) => {
  const supabase = createClient(
    Deno.env.get("URL"),
    Deno.env.get("ANON_KEY")
  );

  const data = await req.json();
  const { analysis, transcript, call_id, summary } = data;

  console.log("Analysis received:", analysis);

  try {
    const { error: analysisError, data: analysisData } = await supabase
      .from("call_analysis")
      .insert({
        call_id: call_id,
        user_id: "c9931794-b648-469f-b02c-14ed048edaba",
        call_sentiment: analysis.sentiment,
        complaint: analysis.complaint,
        compliment: analysis.compliment,
        products_mentioned: analysis.products_mentioned,
        actionable_suggestions: analysis.actionable_suggestions,
        urgency: analysis.urgency,
        topics: analysis.topics,
        customer_satisfaction_score: analysis.customer_satisfaction_score,
        call_summary: summary,
      });

    if (analysisError) {
      return new Response(JSON.stringify({ error: analysisError.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    } else {
      console.log("Analysis inserted:", analysisData);
    }
  } catch (error) {
    console.error("Unexpected error inserting analysis:", error);
  }

  return new Response(JSON.stringify({ records: data }), {
    headers: { "Content-Type": "application/json" },
  });
});
