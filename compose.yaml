services:
  typescript-app:
    build:
      context: .
    container_name: sonexa-app
    restart: unless-stopped
    init: true
    # env_file: ./.env  # Uncomment if .env file exists
    ports:
      - "3000:3000"  # Expose Next.js app
    # If you add a database or other services, add depends_on here
    # networks: [appnet]  # Uncomment if you add more services/networks

# No external database or cache service detected in README or codebase.
# If you add a database (e.g., PostgreSQL, Redis), add it here and configure networking/volumes.

# networks:
#   appnet:
#     driver: bridge
