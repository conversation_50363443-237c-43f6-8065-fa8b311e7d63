# Node.js dependencies
node_modules/

# Next.js build output
.next/

# TypeScript build output (if any)
dist/
build/
out/

# Version control
.git/
.gitignore

# IDE/editor settings
.vscode/
.idea/
*.swp
*.swo

# Environment and secrets
.env*
*.env
*.pem
*.key
*.crt

# Temporary and cache files
tmp/
temp/
*.tmp
*.log
npm-debug.log
yarn-debug.log

# Documentation
README*
*.md
docs/

# Test and debug artifacts
test/
tests/
debug/

# Project-specific
.local/
local/

# Supabase local state and temp
supabase/.branches/
supabase/.temp/

# Miscellaneous
*.DS_Store
*.bak

# Ignore Python, Go, Java, and other language-specific artifacts (for completeness)
__pycache__/
*.pyc
*.pyo
.pytest_cache/
.coverage
/vendor/
*.test
.go-cache/
target/
*.class
.gradle/
.svn/
.hg/
