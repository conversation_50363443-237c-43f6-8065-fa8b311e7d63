{"name": "sonexa-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:introspect": "drizzle-kit introspect", "db:studio": "drizzle-kit studio", "db:up": "drizzle-kit up", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@auth/supabase-adapter": "^1.9.1", "@next/bundle-analyzer": "^15.4.0-canary.51", "@radix-ui/react-slot": "^1.2.3", "@sentry/nextjs": "^9.40.0", "@supabase/supabase-js": "^2.49.7", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.79.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.41.0", "lucide-react": "^0.511.0", "motion": "^12.13.0", "mysql2": "^3.11.0", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "openai": "^5.5.1", "pg": "^8.16.0", "postgres": "^3.4.5", "react": "^19.0.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "tailwind-merge": "^3.3.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/pg": "^8.15.2", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "drizzle-kit": "^0.30.6", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "supabase": "^2.26.9", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "tw-animate-css": "^1.3.3", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}