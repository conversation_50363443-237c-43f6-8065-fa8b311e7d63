import withBundleAnalyzer from "@next/bundle-analyzer";
import type { NextConfig } from "next";
import { withSentryConfig } from "@sentry/nextjs";

const baseConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        pathname: "/**",
      },
    ],
  },
};

const sentryWebpackOptions = {
  org: "sonexa",
  project: "javascript-nextjs",
  silent: !process.env.CI,
  widenClientFileUpload: true,
  tunnelRoute: "/monitoring",
  disableLogger: true,
  automaticVercelMonitors: true,
};

const sentryWrappedConfig = withSentryConfig(baseConfig, sentryWebpackOptions);

export default withBundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
})(sentryWrappedConfig);
