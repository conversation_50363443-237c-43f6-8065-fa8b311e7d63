ALTER TABLE "call_analysis" RENAME COLUMN "business_id" TO "userId";--> statement-breakpoint
ALTER TABLE "call_analysis" DROP CONSTRAINT "call_analysis_business_id_fkey";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_business_id_fkey";
--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "business_id" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "call_sentiment" text NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "complaint" boolean NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "compliment" boolean NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "products_mentioned" text[] NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "actionable_suggestions" text[] NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "urgency" text NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "topics" text[] NOT NULL;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD COLUMN "customer_satisfaction_score" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "businesses" ADD CONSTRAINT "businesses_id_fkey" FOREIGN KEY ("id") REFERENCES "public"."user"("business_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "call_analysis" ADD CONSTRAINT "call_analysis_user_id_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "call_analysis" DROP COLUMN "analysis";--> statement-breakpoint
ALTER TABLE "call_analysis" ADD CONSTRAINT "customer_satisfaction_check" CHECK (customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10);