import { relations } from "drizzle-orm/relations";
import { users, calls, callAnalysis } from "./schema";

export const callsRelations = relations(calls, ({one}) => ({
	user: one(users, {
		fields: [calls.userId],
		references: [users.id]
	}),
}));

export const usersRelations = relations(users, ({many}) => ({
	calls: many(calls),
	callAnalyses: many(callAnalysis),
}));

export const callAnalysisRelations = relations(callAnalysis, ({one}) => ({
	user: one(users, {
		fields: [callAnalysis.userId],
		references: [users.id]
	}),
}));