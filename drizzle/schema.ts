import { pgTable, foreignKey, unique, pgPolicy, text, timestamp, uuid } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text(),
	email: text(),
	emailVerified: timestamp({ mode: 'string' }),
	image: text(),
	businessId: uuid("business_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.businessId],
			foreignColumns: [businesses.id],
			name: "user_business_id_fkey"
		}).onDelete("cascade"),
	unique("user_email_unique").on(table.email),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"], using: sql`true` }),
]);

export const businesses = pgTable("businesses", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	email: text(),
	phone: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"], using: sql`true` }),
]);

export const callAnalysis = pgTable("call_analysis", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	callId: text().notNull(),
	businessId: uuid("business_id").notNull(),
	analysis: text().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.businessId],
			foreignColumns: [businesses.id],
			name: "call_analysis_business_id_fkey"
		}),
	pgPolicy("Allow all for now", { as: "permissive", for: "all", to: ["public"] }),
]);
