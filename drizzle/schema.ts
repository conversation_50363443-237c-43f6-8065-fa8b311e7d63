import { pgTable, unique, uuid, text, timestamp, index, foreignKey, boolean, check, bigserial, jsonb, integer } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const users = pgTable("users", {
	id: uuid().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	unique("users_email_key").on(table.email),
]);

export const calls = pgTable("calls", {
	id: uuid().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	status: text("status", { 
		enum: ["new", "in_progress", "completed", "failed", "cancelled"] 
	}).notNull().default("new"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_calls_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_calls_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "calls_user_id_fkey"
		}).onDelete("cascade"),
]);

export const callAnalysis = pgTable("call_analysis", {
	id: bigserial({ mode: "bigint" }).primaryKey().notNull(),
	callId: uuid("call_id").notNull(),
	userId: uuid("user_id").notNull(),
	callSummary: text("call_summary"),
	callSentiment: text("call_sentiment"),
	complaint: boolean().default(false).notNull(),
	compliment: boolean().default(false).notNull(),
	productsMentioned: jsonb("products_mentioned").default([]),
	actionableSuggestions: jsonb("actionable_suggestions").default([]),
	urgency: text(),
	topics: jsonb().default([]),
	customerSatisfactionScore: integer("customer_satisfaction_score"),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_call_analysis_call_id").using("btree", table.callId.asc().nullsLast().op("uuid_ops")),
	index("idx_call_analysis_created_at").using("btree", table.createdAt.asc().nullsLast().op("timestamptz_ops")),
	index("idx_call_analysis_satisfaction_score").using("btree", table.customerSatisfactionScore.asc().nullsLast().op("int4_ops")),
	index("idx_call_analysis_sentiment").using("btree", table.callSentiment.asc().nullsLast().op("text_ops")),
	index("idx_call_analysis_urgency").using("btree", table.urgency.asc().nullsLast().op("text_ops")),
	index("idx_call_analysis_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "call_analysis_user_id_fkey"
		}).onDelete("cascade"),
	check("call_analysis_call_sentiment_check", sql`call_sentiment = ANY (ARRAY['positive'::text, 'neutral'::text, 'negative'::text])`),
	check("call_analysis_urgency_check", sql`urgency = ANY (ARRAY['low'::text, 'medium'::text, 'high'::text])`),
	check("call_analysis_customer_satisfaction_score_check", sql`(customer_satisfaction_score >= 1) AND (customer_satisfaction_score <= 10)`),
]);
