{"id": "ca29c62f-3d5c-47c4-ad72-e71e4e2d4798", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.businesses": {"name": "businesses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.call_analysis": {"name": "call_analysis", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "callId": {"name": "callId", "type": "text", "primaryKey": false, "notNull": true}, "business_id": {"name": "business_id", "type": "uuid", "primaryKey": false, "notNull": true}, "analysis": {"name": "analysis", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"call_analysis_business_id_fkey": {"name": "call_analysis_business_id_fkey", "tableFrom": "call_analysis", "tableTo": "businesses", "columnsFrom": ["business_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "business_id": {"name": "business_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_business_id_fkey": {"name": "user_business_id_fkey", "tableFrom": "user", "tableTo": "businesses", "columnsFrom": ["business_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}