{"id": "cc4688fd-f84c-4cf9-980e-6e1fa97d02a5", "prevId": "8b06f44f-c5ff-48ee-86ba-f09e0a2f2eba", "version": "7", "dialect": "postgresql", "tables": {"public.businesses": {"name": "businesses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.call_analysis": {"name": "call_analysis", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "callId": {"name": "callId", "type": "text", "primaryKey": false, "notNull": true}, "call_sentiment": {"name": "call_sentiment", "type": "text", "primaryKey": false, "notNull": true}, "complaint": {"name": "complaint", "type": "boolean", "primaryKey": false, "notNull": true}, "compliment": {"name": "compliment", "type": "boolean", "primaryKey": false, "notNull": true}, "products_mentioned": {"name": "products_mentioned", "type": "text[]", "primaryKey": false, "notNull": false}, "actionable_suggestions": {"name": "actionable_suggestions", "type": "text[]", "primaryKey": false, "notNull": false}, "urgency": {"name": "urgency", "type": "text", "primaryKey": false, "notNull": true}, "topics": {"name": "topics", "type": "text[]", "primaryKey": false, "notNull": false}, "customer_satisfaction_score": {"name": "customer_satisfaction_score", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"call_analysis_user_id_fkey": {"name": "call_analysis_user_id_fkey", "tableFrom": "call_analysis", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {"customer_satisfaction_check": {"name": "customer_satisfaction_check", "value": "customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10"}}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "business_id": {"name": "business_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_business_id_fkey": {"name": "user_business_id_fkey", "tableFrom": "user", "tableTo": "businesses", "columnsFrom": ["business_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}