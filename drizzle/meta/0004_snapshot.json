{"id": "30ca6c53-0b68-4e0c-afa8-bf64a9663afa", "prevId": "cc4688fd-f84c-4cf9-980e-6e1fa97d02a5", "version": "7", "dialect": "postgresql", "tables": {"public.call_analysis": {"name": "call_analysis", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "call_id": {"name": "call_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "call_summary": {"name": "call_summary", "type": "text", "primaryKey": false, "notNull": false}, "call_sentiment": {"name": "call_sentiment", "type": "text", "primaryKey": false, "notNull": true}, "complaint": {"name": "complaint", "type": "boolean", "primaryKey": false, "notNull": true}, "compliment": {"name": "compliment", "type": "boolean", "primaryKey": false, "notNull": true}, "products_mentioned": {"name": "products_mentioned", "type": "text[]", "primaryKey": false, "notNull": false}, "actionable_suggestions": {"name": "actionable_suggestions", "type": "text[]", "primaryKey": false, "notNull": false}, "urgency": {"name": "urgency", "type": "text", "primaryKey": false, "notNull": true}, "topics": {"name": "topics", "type": "text[]", "primaryKey": false, "notNull": false}, "customer_satisfaction_score": {"name": "customer_satisfaction_score", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"call_analysis_user_id_fkey": {"name": "call_analysis_user_id_fkey", "tableFrom": "call_analysis", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "call_analysis_call_id_fkey": {"name": "call_analysis_call_id_fkey", "tableFrom": "call_analysis", "tableTo": "calls", "columnsFrom": ["call_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"]}}, "checkConstraints": {"customer_satisfaction_check": {"name": "customer_satisfaction_check", "value": "customer_satisfaction_score >= 1 AND customer_satisfaction_score <= 10"}}, "isRLSEnabled": false}, "public.calls": {"name": "calls", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"calls_user_id_fkey": {"name": "calls_user_id_fkey", "tableFrom": "calls", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"Allow all for now": {"name": "Allow all for now", "as": "PERMISSIVE", "for": "ALL", "to": ["public"], "using": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}